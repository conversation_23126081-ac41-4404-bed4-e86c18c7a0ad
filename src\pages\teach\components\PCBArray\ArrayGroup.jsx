import { Button, Dropdown } from 'antd';
import React from 'react';
import { useTranslation } from 'react-i18next';
import _ from 'lodash';
import { useGoldenRegisterArrayMutation } from '../../../../services/product';
import { newSubArrayBoardSelectionRoiOffset } from '../../../../common/const';
import { ALERT_TYPES, aoiAlert } from '../../../../common/alert';
import { useDispatch } from 'react-redux';
import { setContainerLvlLoadingMsg, setIsContainerLvlLoadingEnabled } from '../../../../reducer/setting';


const ArrayGroup = (props) => {
  const {
    arrayRegisteration,
    setSelectedTool,
    leftColHeight,
    curProduct,
    refetchArrayRegisteration,
    handleRefetchAllComponents,
    handleRefetchAllFeatures,
    setActiveTab,
    setPanZoomToArrayBoardSelectionIdx,
  } = props;

  const dispatch = useDispatch();

  const { t } = useTranslation();

  const [goldenRegisterArray] = useGoldenRegisterArrayMutation();

  const handleRemoveAllArrayBoardSelectionRoi = async (
    curProduct,
  ) => {
    const payload = {
      product_id: Number(_.get(curProduct, 'product_id', 0)),
      step: 0,
      array_transforms: [],
      selection_roi: {
        points: [
          { x: 0, y: 0 },
          { x: 0, y: 0 },
        ],
        angle: 0,
        type: 'obb',
      },
      component_ids: [],
    };

    dispatch(setIsContainerLvlLoadingEnabled(true));
    dispatch(setContainerLvlLoadingMsg(t('loader.registeringGoldenArray')));

    const res = await goldenRegisterArray(payload);

    if (res.error) {
      aoiAlert(t('notification.error.failedToRemoveArrayGroup'), ALERT_TYPES.COMMON_ERROR);
      console.error('goldenRegisterArray error:', _.get(res, 'error.message', ''));
      dispatch(setIsContainerLvlLoadingEnabled(false));
      dispatch(setContainerLvlLoadingMsg(''));
      return;
    }

    await refetchArrayRegisteration();
    await handleRefetchAllComponents(
      Number(_.get(curProduct, 'product_id', 0)),
      0,
    );
    await handleRefetchAllFeatures(
      Number(_.get(curProduct, 'product_id', 0)),
      0,
    );

    dispatch(setIsContainerLvlLoadingEnabled(false));
    dispatch(setContainerLvlLoadingMsg(''));
  };

  const handleAddNewArrayBoardSelectionRoi = async (
    arrayRegisteration,
    curProduct,
  ) => {
    const arrayIndexList = _.map(arrayRegisteration.array_transforms, t => t.array_index);
    const maxArrayIndex = _.max(arrayIndexList) + 1;

    const selectionRoiDim = {
      width: _.get(arrayRegisteration, 'selection_roi.points[1].x', 0) - _.get(arrayRegisteration, 'selection_roi.points[0].x', 0),
      height: _.get(arrayRegisteration, 'selection_roi.points[1].y', 0) - _.get(arrayRegisteration, 'selection_roi.points[0].y', 0),
    }

    const payload = {
      product_id: Number(_.get(curProduct, 'product_id', 0)),
      step: 0,
      array_transforms: [
        ..._.get(arrayRegisteration, 'array_transforms', []),
        {
          array_index: maxArrayIndex,
          flip: {
            x: false,
            y: false,
            center: {
              x: selectionRoiDim.width / 2 + newSubArrayBoardSelectionRoiOffset + selectionRoiDim.width,
              y: selectionRoiDim.height / 2 + newSubArrayBoardSelectionRoiOffset + selectionRoiDim.height,
            }
          },
          rotation: {
            angle: 0,
            center: {
              x: selectionRoiDim.width / 2 + newSubArrayBoardSelectionRoiOffset + selectionRoiDim.width,
              y: selectionRoiDim.height / 2 + newSubArrayBoardSelectionRoiOffset + selectionRoiDim.height,
            }
          },
          translation: {
            x: newSubArrayBoardSelectionRoiOffset,
            y: newSubArrayBoardSelectionRoiOffset,
          },
        }
      ],
      selection_roi: _.get(arrayRegisteration, 'selection_roi', {}),
      component_ids: _.get(arrayRegisteration, 'component_ids', []),
    };

    dispatch(setIsContainerLvlLoadingEnabled(true));
    dispatch(setContainerLvlLoadingMsg(t('loader.registeringGoldenArray')));

    const res = await goldenRegisterArray(payload);

    if (res.error) {
      aoiAlert(t('notification.error.registerGoldenArray'), ALERT_TYPES.COMMON_ERROR);
      console.error('goldenRegisterArray error:', _.get(res, 'error.message', ''));
      return;
    }

    await refetchArrayRegisteration();
    await handleRefetchAllComponents(
      Number(_.get(curProduct, 'product_id', 0)),
      0,
    );

    dispatch(setIsContainerLvlLoadingEnabled(false));
    dispatch(setContainerLvlLoadingMsg(''));
  };

  if (_.isEmpty(_.get(arrayRegisteration, 'array_transforms', []))) {
    return (
      <div className='flex flex-col flex-1 gap-6 self-stretch justify-center'>
        <div className='flex px-4 flex-col items-center justify-center gap-4 self-stretch'>
          <span className='font-source text-[14px] font-normal leading-[150%]'>
            {t('productDefine.createInitialArrayUnit')}
          </span>
          <span className='font-source text-[12px] font-normal leading-[150%] text-center'>
            {t('productDefine.selectAllRelatedComponent')}
          </span>
        </div>
        <div className='flex flex-col items-center justify-center p-4 self-stretch'>
          <Button
            style={{ width: '100%' }}
            type='primary'
            onClick={() => {
              setSelectedTool('defineSelectionRoi');
            }}
          >
            <span className='font-source text-[12px] font-semibold leading-[normal] text-gray-1'>
              {t('productDefine.createArrayUnit')}
            </span>
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div
      className='flex flex-col items-start gap-6 self-stretch overflow-y-auto'
      style={{ height: `${leftColHeight}px` }}
    >
      <div className='flex flex-col items-start self-stretch'>
        {_.map(_.get(arrayRegisteration, 'array_transforms', []), (arrayTransform, idx) => {
          return (
            <div
              className={`flex flex-col items-start gap-0.5 self-stretch px-0 py-1 rounded-sm border-b-[rgba(255,255,255,0.06)]
                border-b border-solid cursor-pointer hover:bg-[#ffffff1a] transition-all duration-300 ease-in-out`}
              key={idx}
              onClick={() => {
                setPanZoomToArrayBoardSelectionIdx(arrayTransform.array_index);
              }}
            >
              <div
                className='flex h-9 items-center gap-2 self-stretch pl-3 pr-2 py-1.5'
              >
                <span className='flex-1 text-[#F4E76E] font-source text-xs font-normal leading-[150%]'>
                  {t('productDefine.arrayBoard')}-{arrayTransform.array_index + 1}
                </span>
                {arrayTransform.array_index === 0 ?
                  <div
                    className='flex w-6 h-6 justify-center items-center gap-2.5 p-0.5 cursor-pointer'
                    onClick={() => {
                      handleAddNewArrayBoardSelectionRoi(
                        arrayRegisteration,
                        curProduct,
                      );
                    }}
                  >
                    <img
                      src='/icn/addNew_gray.svg'
                      alt='addNew'
                      className='w-3.5 h-3.5'
                    />
                  </div>
                :
                  <div className='flex w-6 h-6 justify-center items-center gap-2.5 p-0.5' />
                }
                <Dropdown
                  menu={{
                    items: [
                      {
                        key: 'delete',
                        label: <span className='font-source text-[12px] font-normal leading-[150%]'>
                          {t('common.delete')}
                        </span>,
                        disabled: arrayTransform.array_index === 0,
                        onClick: () => {
                          const payload = {
                            product_id: Number(_.get(curProduct, 'product_id', 0)),
                            step: 0,
                            selection_roi: _.get(arrayRegisteration, 'selection_roi', {}),
                            component_ids: _.get(arrayRegisteration, 'component_ids', []),
                          };
                          payload.array_transforms = _.filter(_.get(arrayRegisteration, 'array_transforms', []), t => t.array_index !== arrayTransform.array_index);

                          const submit = async (payload) => {
                            dispatch(setIsContainerLvlLoadingEnabled(true));
                            dispatch(setContainerLvlLoadingMsg(t('loader.registeringGoldenArray')));

                            const res = await goldenRegisterArray(payload);

                            if (res.error) {
                              aoiAlert(t('notification.error.registerGoldenArray'), ALERT_TYPES.COMMON_ERROR);
                              console.error('goldenRegisterArray error:', _.get(res, 'error.message', ''));
                              return;
                            }

                            await refetchArrayRegisteration();
                            await handleRefetchAllComponents(
                              payload.product_id,
                              0,
                            );
                            await handleRefetchAllFeatures(
                              payload.product_id,
                              0,
                            );

                            dispatch(setIsContainerLvlLoadingEnabled(false));
                            dispatch(setContainerLvlLoadingMsg(''));
                          };

                          submit(payload);
                        },
                      },
                    ]
                  }}
                >
                  <div className='flex w-6 h-6 justify-center items-center gap-1 self-stretch rounded px-2 py-0'>
                    <img src='/icn/ellipsis_white.svg' alt='ellipsis' className='h-[10px] w-[14px]' />
                  </div>
                </Dropdown>
              </div>
            </div>
          );
        })}
      </div>
      <div className='flex justify-center items-center gap-2 self-stretch p-4 flex-col'>
        {_.get(arrayRegisteration, 'array_transforms', []).length > 0 && (
          <Button
            style={{ width: '100%' }}
            onClick={() => {
              handleRemoveAllArrayBoardSelectionRoi(curProduct);
            }}
          >
            <span className='font-source text-[12px] font-normal leading-[150%]'>
              {t('productDefine.removeAllArrayBoard')}
            </span>
          </Button>
        )}
        <div className='flex justify-center items-center gap-2 self-stretch'>
          {_.get(arrayRegisteration, 'array_transforms', []).length > 0 && (
            <Button
              style={{ width: '100%' }}
              onClick={() => setSelectedTool('defineSelectionRoi')}
            >
              <span className='font-source text-[12px] font-normal leading-[150%]'>
                {t('productDefine.redefineSelectionRoi')}
              </span>
            </Button>
          )}
          <Button
            style={{ width: '100%' }}
            onClick={() => setActiveTab && setActiveTab('smartPCBArray')}
            disabled={_.get(arrayRegisteration, 'array_transforms', []).length === 0}
          >
            <span className='font-source text-[12px] font-normal leading-[150%]'>
              {t('productDefine.useSmartArrayTool')}
            </span>
          </Button>
        </div>
      </div>
    </div>
  );
};

export default ArrayGroup;