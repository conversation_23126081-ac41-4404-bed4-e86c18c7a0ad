import { But<PERSON>, Checkbox, InputNumber, Tooltip } from 'antd';
import React, { useEffect, useState, useRef, Fragment } from 'react';
import { useTranslation } from 'react-i18next';
import { ALERT_TYPES, aoiAlert } from '../../../../common/alert';
import _ from 'lodash';
import { useGoldenRegisterArrayMutation } from '../../../../services/product';
import { arrayBoardSelectionRoiStrokeWidth } from '../../../../common/const';
import { useDispatch } from 'react-redux';
import { setContainerLvlLoadingMsg, setIsContainerLvlLoadingEnabled } from '../../../../reducer/setting';
import { rotatePoint } from '../../../../viewer/util';


const SmartPCBArray = (props) => {
  const {
    arrayRegisteration,
    setActiveTab,
    curSelectedMarker,
    setCurSelectedMarker,
    setSelectedTool,
    curProduct,
    refetchArrayRegisteration,
    handleRefetchAllComponents,
    curMarkers,
    setCurMarkers,
    isSubBoardSelectionRoiDisplayed,
    setIsSubBoardSelectionRoiDisplayed,
    setPreviewArrayTransforms,
    setComponentsActiveTab,
    curPairModeMarkers,
    setCurPairModeMarkers,
  } = props;

  const dispatch = useDispatch();

  const { t } = useTranslation();

  const curSelectingMarker = useRef(null);
  const isCurSelectingPairMode = useRef(false);
  const [gridRowCount, setGridRowCount] = useState(2);
  const [gridColumnCount, setGridColumnCount] = useState(2);
  const [pairMode, setPairMode] = useState(false);
  const [pairModeAngle, setPairModeAngle] = useState(180);
  const [pairModeRowCount, setPairModeRowCount] = useState(2);
  const [pairModeColumnCount, setPairModeColumnCount] = useState(2);

  const [goldenRegisterArray] = useGoldenRegisterArrayMutation();

  useEffect(() => {
    if (!setPreviewArrayTransforms) return;

    // if (!curMarkers.tl || !curMarkers.tr || !curMarkers.bl) {
    //   setPreviewArrayTransforms(null);
    //   return;
    // }

    if (
      !curMarkers.tl ||
      (gridRowCount > 1 && !curMarkers.tr) ||
      (gridColumnCount > 1 && !curMarkers.bl)
    ) {
      setPreviewArrayTransforms(null);
      return;
    }

    // const markerWidth = curMarkers.tr.x - curMarkers.tl.x;
    // const markerHeight = curMarkers.bl.y - curMarkers.tl.y;

    const markerWidth = gridRowCount > 1 ? curMarkers.tr.x - curMarkers.tl.x : 0;
    const markerHeight = gridColumnCount > 1 ? curMarkers.bl.y - curMarkers.tl.y : 0;

    // if (markerWidth <= 0 || markerHeight <= 0) {
    //   setPreviewArrayTransforms(null);
    //   return;
    // }

    if (
      (markerWidth <= 0 && gridRowCount > 1) ||
      (markerHeight <= 0 && gridColumnCount > 1)
    ) {
      setPreviewArrayTransforms(null);
      return;
    }

    const singleSubBoardWidth = gridRowCount > 1 ? markerWidth / (gridRowCount - 1) : markerWidth;
    const singleSubBoardHeight = gridColumnCount > 1 ? markerHeight / (gridColumnCount - 1) : markerHeight;

    const tlblxInterval = gridColumnCount > 1 ? (curMarkers.bl.x - curMarkers.tl.x) / (gridColumnCount - 1) : 0;
    const tltryInterval = gridRowCount > 1 ? (curMarkers.tr.y - curMarkers.tl.y) / (gridRowCount - 1) : 0;

    const selectionRoiDimension = {
      width: _.get(arrayRegisteration, 'selection_roi.points[1].x', 0) - _.get(arrayRegisteration, 'selection_roi.points[0].x', 0),
      height: _.get(arrayRegisteration, 'selection_roi.points[1].y', 0) - _.get(arrayRegisteration, 'selection_roi.points[0].y', 0),
    };

    const previewTransforms = [_.find(arrayRegisteration.array_transforms, t => t.array_index === 0)];

    // Generate regular mode transforms
    for (let i = 1; i <= gridRowCount; i++) {
      for (let j = 1; j <= gridColumnCount; j++) {
        if (i === 1 && j === 1) continue;
        previewTransforms.push({
          array_index: previewTransforms.length,
          flip: {
            x: false,
            y: false,
            center: {
              x: (i - 1) * singleSubBoardWidth + selectionRoiDimension.width / 2 + (j - 1) * tlblxInterval + _.get(arrayRegisteration, 'selection_roi.points[0].x', 0),
              y: (j - 1) * singleSubBoardHeight + selectionRoiDimension.height / 2 + (i - 1) * tltryInterval + _.get(arrayRegisteration, 'selection_roi.points[0].y', 0),
            }
          },
          rotation: {
            angle: 0,
            center: {
              x: (i - 1) * singleSubBoardWidth + selectionRoiDimension.width / 2 + (j - 1) * tlblxInterval + _.get(arrayRegisteration, 'selection_roi.points[0].x', 0),
              y: (j - 1) * singleSubBoardHeight + selectionRoiDimension.height / 2 + (i - 1) * tltryInterval + _.get(arrayRegisteration, 'selection_roi.points[0].y', 0),
            }
          },
          translation: {
            x: (i - 1) * singleSubBoardWidth + (j - 1) * tlblxInterval,
            y: (j - 1) * singleSubBoardHeight + (i - 1) * tltryInterval,
          },
        });
      }
    }

    // Generate pair mode (mirror mode) transforms if enabled
    if (pairMode && curPairModeMarkers.tl) {
      // Check pair mode marker requirements
      const pairModeValid = (
        curPairModeMarkers.tl &&
        (pairModeRowCount <= 1 || curPairModeMarkers.tr) &&
        (pairModeColumnCount <= 1 || curPairModeMarkers.bl)
      );

      if (pairModeValid) {
        const pairMarkerWidth = pairModeRowCount > 1 ? curPairModeMarkers.tr.x - curPairModeMarkers.tl.x : 0;
        const pairMarkerHeight = pairModeColumnCount > 1 ? curPairModeMarkers.bl.y - curPairModeMarkers.tl.y : 0;

        // Validate marker positions
        const pairMarkersValid = (
          (pairModeRowCount <= 1 || pairMarkerWidth > 0) &&
          (pairModeColumnCount <= 1 || pairMarkerHeight > 0)
        );

        if (pairMarkersValid) {
          const pairSingleSubBoardWidth = pairModeRowCount > 1 ? pairMarkerWidth / (pairModeRowCount - 1) : pairMarkerWidth;
          const pairSingleSubBoardHeight = pairModeColumnCount > 1 ? pairMarkerHeight / (pairModeColumnCount - 1) : pairMarkerHeight;

          const pairTlblxInterval = pairModeColumnCount > 1 ? (curPairModeMarkers.bl.x - curPairModeMarkers.tl.x) / (pairModeColumnCount - 1) : 0;
          const pairTltryInterval = pairModeRowCount > 1 ? (curPairModeMarkers.tr.y - curPairModeMarkers.tl.y) / (pairModeRowCount - 1) : 0;

          const rotatedCurMarkerTl = rotatePoint(curMarkers.tl, pairModeAngle, _.get(arrayRegisteration, 'array_transforms[0].rotation.center', {}));

          const pairModeOffset = {
            x: curPairModeMarkers.tl.x - rotatedCurMarkerTl.x,
            y: curPairModeMarkers.tl.y - rotatedCurMarkerTl.y,
          };

          for (let i = 1; i <= pairModeRowCount; i++) {
            for (let j = 1; j <= pairModeColumnCount; j++) {
              const translationX = pairModeOffset.x + (i - 1) * pairSingleSubBoardWidth + (j - 1) * pairTlblxInterval;
              const translationY = pairModeOffset.y + (j - 1) * pairSingleSubBoardHeight + (i - 1) * pairTltryInterval;

              previewTransforms.push({
                array_index: previewTransforms.length,
                flip: {
                  x: false,
                  y: false,
                  center: {
                    x: selectionRoiDimension.width / 2 + translationX + _.get(arrayRegisteration, 'selection_roi.points[0].x', 0),
                    y: selectionRoiDimension.height / 2 + translationY + _.get(arrayRegisteration, 'selection_roi.points[0].y', 0),
                  }
                },
                rotation: {
                  angle: pairModeAngle,
                  center: {
                    x: selectionRoiDimension.width / 2 + translationX + _.get(arrayRegisteration, 'selection_roi.points[0].x', 0),
                    y: selectionRoiDimension.height / 2 + translationY + _.get(arrayRegisteration, 'selection_roi.points[0].y', 0),
                  }
                },
                translation: {
                  x: translationX,
                  y: translationY,
                },
              });
            }
          }
        }
      }
    }

    // console.log('Preview transforms generated:', previewTransforms);
    // console.log('Pair mode enabled:', pairMode, 'Pair mode markers:', curPairModeMarkers);
    setPreviewArrayTransforms(previewTransforms);
  }, [curMarkers, gridRowCount, gridColumnCount, arrayRegisteration, pairMode, curPairModeMarkers, pairModeAngle, pairModeRowCount, pairModeColumnCount]);

  const handleGenerateArraySubmit = async (
    arrayRegisteration,
    gridRowCount,
    gridColumnCount,
    curMarkers,
    curProduct,
    step,
    handleRefetchAllComponents,
    refetchArrayRegisteration,
    pairMode,
    pairModeAngle,
    pairModeRowCount,
    pairModeColumnCount,
    curPairModeMarkers,
  ) => {
    // if (!curMarkers.tl || !curMarkers.tr || !curMarkers.bl) {
    //   aoiAlert(t('notification.error.pleaseDefineThreeMarkers'), ALERT_TYPES.COMMON_ERROR);
    //   return;
    // }

    if (!curMarkers.tl) {
      aoiAlert(t('notification.error.pleaseDefineTopLeftMarker'), ALERT_TYPES.COMMON_ERROR);
      return;
    }

    if (gridColumnCount > 1 && !curMarkers.bl) {
      aoiAlert(t('notification.error.pleaseDefineBottomLeftMarker'), ALERT_TYPES.COMMON_ERROR);
      return;
    }

    if (gridRowCount > 1 && !curMarkers.tr) {
      aoiAlert(t('notification.error.pleaseDefineTopRightMarker'), ALERT_TYPES.COMMON_ERROR);
      return;
    }

    // check if tl is on the left of tr
    if (gridRowCount > 1 && curMarkers.tl.x >= curMarkers.tr.x) {
      aoiAlert(t('notification.error.invalidMarkerPoint'), ALERT_TYPES.COMMON_ERROR);
      return;
    }

    // check if bl is below tl
    if (gridColumnCount > 1 && curMarkers.bl.y <= curMarkers.tl.y) {
      aoiAlert(t('notification.error.invalidMarkerPoint'), ALERT_TYPES.COMMON_ERROR);
      return;
    }

    const markerWidth = gridRowCount > 1 ? curMarkers.tr.x - curMarkers.tl.x : 0;
    const markerHeight = gridColumnCount > 1 ? curMarkers.bl.y - curMarkers.tl.y : 0;

    const singleSubBoardWidth = gridRowCount > 1 ? markerWidth / (gridRowCount - 1) : markerWidth;
    const singleSubBoardHeight = gridColumnCount > 1 ? markerHeight / (gridColumnCount - 1) : markerHeight;

    const tlblxInterval = gridColumnCount > 1 ? (curMarkers.bl.x - curMarkers.tl.x) / (gridColumnCount - 1) : 0;
    const tltryInterval = gridRowCount > 1 ? (curMarkers.tr.y - curMarkers.tl.y) / (gridRowCount - 1) : 0;

    const selectionRoiDimension = {
      width: _.get(arrayRegisteration, 'selection_roi.points[1].x', 0) - _.get(arrayRegisteration, 'selection_roi.points[0].x', 0) + 1,
      height: _.get(arrayRegisteration, 'selection_roi.points[1].y', 0) - _.get(arrayRegisteration, 'selection_roi.points[0].y', 0) + 1,
    };

    const arrayTransforms = [_.find(arrayRegisteration.array_transforms, t => t.array_index === 0)];

    // Generate regular mode transforms
    for (let i = 1; i <= gridRowCount; i++) {
      for (let j = 1; j <= gridColumnCount; j++) {
        if (i === 1 && j === 1) continue;
        arrayTransforms.push({
          array_index: arrayTransforms.length,
          flip: {
            x: false,
            y: false,
            center: {
              x: (i - 1) * singleSubBoardWidth + selectionRoiDimension.width / 2 + (j - 1) * tlblxInterval + _.get(arrayRegisteration, 'selection_roi.points[0].x', 0),
              y: (j - 1) * singleSubBoardHeight + selectionRoiDimension.height / 2 + (i - 1) * tltryInterval + _.get(arrayRegisteration, 'selection_roi.points[0].y', 0),
            }
          },
          rotation: {
            angle: 0,
            center: {
              x: (i - 1) * singleSubBoardWidth + selectionRoiDimension.width / 2 + (j - 1) * tlblxInterval + _.get(arrayRegisteration, 'selection_roi.points[0].x', 0),
              y: (j - 1) * singleSubBoardHeight + selectionRoiDimension.height / 2 + (i - 1) * tltryInterval + _.get(arrayRegisteration, 'selection_roi.points[0].y', 0),
            }
          },
          translation: {
            x: (i - 1) * singleSubBoardWidth + (j - 1) * tlblxInterval,
            y: (j - 1) * singleSubBoardHeight + (i - 1) * tltryInterval,
          },
        });
      }
    }

    // Generate pair mode (mirror mode) transforms if enabled
    if (pairMode && curPairModeMarkers.tl) {
      // Validate pair mode markers
      if (pairModeRowCount > 1 && !curPairModeMarkers.tr) {
        aoiAlert(t('notification.error.pleaseDefineTopRightMarker') + ' (' + t('productDefine.pairMode') + ')', ALERT_TYPES.COMMON_ERROR);
        return;
      }

      if (pairModeColumnCount > 1 && !curPairModeMarkers.bl) {
        aoiAlert(t('notification.error.pleaseDefineBottomLeftMarker') + ' (' + t('productDefine.pairMode') + ')', ALERT_TYPES.COMMON_ERROR);
        return;
      }

      const pairMarkerWidth = pairModeRowCount > 1 ? curPairModeMarkers.tr.x - curPairModeMarkers.tl.x : 0;
      const pairMarkerHeight = pairModeColumnCount > 1 ? curPairModeMarkers.bl.y - curPairModeMarkers.tl.y : 0;

      // Validate pair mode marker positions
      if (pairModeRowCount > 1 && pairMarkerWidth <= 0) {
        aoiAlert(t('notification.error.invalidMarkerPoint') + ' (' + t('productDefine.pairMode') + ')', ALERT_TYPES.COMMON_ERROR);
        return;
      }

      if (pairModeColumnCount > 1 && pairMarkerHeight <= 0) {
        aoiAlert(t('notification.error.invalidMarkerPoint') + ' (' + t('productDefine.pairMode') + ')', ALERT_TYPES.COMMON_ERROR);
        return;
      }

      const pairSingleSubBoardWidth = pairModeRowCount > 1 ? pairMarkerWidth / (pairModeRowCount - 1) : pairMarkerWidth;
      const pairSingleSubBoardHeight = pairModeColumnCount > 1 ? pairMarkerHeight / (pairModeColumnCount - 1) : pairMarkerHeight;

      const pairTlblxInterval = pairModeColumnCount > 1 ? (curPairModeMarkers.bl.x - curPairModeMarkers.tl.x) / (pairModeColumnCount - 1) : 0;
      const pairTltryInterval = pairModeRowCount > 1 ? (curPairModeMarkers.tr.y - curPairModeMarkers.tl.y) / (pairModeRowCount - 1) : 0;

      const rotatedCurMarkerTl = rotatePoint(curMarkers.tl, pairModeAngle, _.get(arrayRegisteration, 'array_transforms[0].rotation.center', {}));

      const pairModeOffset = {
        x: curPairModeMarkers.tl.x - rotatedCurMarkerTl.x,
        y: curPairModeMarkers.tl.y - rotatedCurMarkerTl.y,
      };

      for (let i = 1; i <= pairModeRowCount; i++) {
        for (let j = 1; j <= pairModeColumnCount; j++) {
          // Calculate translation: base offset + grid position offset
          const translationX = pairModeOffset.x + (i - 1) * pairSingleSubBoardWidth + (j - 1) * pairTlblxInterval;
          const translationY = pairModeOffset.y + (j - 1) * pairSingleSubBoardHeight + (i - 1) * pairTltryInterval;

          arrayTransforms.push({
            array_index: arrayTransforms.length,
            flip: {
              x: false,
              y: false,
              center: {
                x: selectionRoiDimension.width / 2 + translationX + _.get(arrayRegisteration, 'selection_roi.points[0].x', 0),
                y: selectionRoiDimension.height / 2 + translationY + _.get(arrayRegisteration, 'selection_roi.points[0].y', 0),
              }
            },
            rotation: {
              angle: pairModeAngle,
              center: {
                x: selectionRoiDimension.width / 2 + translationX + _.get(arrayRegisteration, 'selection_roi.points[0].x', 0),
                y: selectionRoiDimension.height / 2 + translationY + _.get(arrayRegisteration, 'selection_roi.points[0].y', 0),
              }
            },
            translation: {
              x: translationX,
              y: translationY,
            },
          });
        }
      }
    }

    const payload = {
      product_id: Number(_.get(curProduct, 'product_id', 0)),
      step,
      selection_roi: _.get(arrayRegisteration, 'selection_roi', {}),
      component_ids: _.get(arrayRegisteration, 'component_ids', []),
      array_transforms: arrayTransforms,
    };

    dispatch(setIsContainerLvlLoadingEnabled(true));
    dispatch(setContainerLvlLoadingMsg(t('loader.registeringGoldenArray')));

    const res = await goldenRegisterArray(payload);

    if (res.error) {
      aoiAlert(t('notification.error.registerGoldenArray'), ALERT_TYPES.COMMON_ERROR);
      console.error('goldenRegisterArray error:', _.get(res, 'error.message', ''));
      dispatch(setIsContainerLvlLoadingEnabled(false));
      dispatch(setContainerLvlLoadingMsg(''));
      return;
    }

    await refetchArrayRegisteration();
    await handleRefetchAllComponents(
      Number(_.get(curProduct, 'product_id', 0)),
      step,
    );

    if (setPreviewArrayTransforms) setPreviewArrayTransforms(null);
    if (setCurSelectedMarker) setCurSelectedMarker(null);
    if (setCurMarkers) setCurMarkers({});
    if (setCurPairModeMarkers) setCurPairModeMarkers({});

    setComponentsActiveTab('templateEditor');

    dispatch(setIsContainerLvlLoadingEnabled(false));
    dispatch(setContainerLvlLoadingMsg(''));
  };

  useEffect(() => {
    if (!curSelectingMarker.current) return;

    if (isCurSelectingPairMode.current) {
      setCurPairModeMarkers({
        ...curPairModeMarkers,
        [curSelectingMarker.current]: curSelectedMarker,
      });
    } else {
      setCurMarkers({
        ...curMarkers,
        [curSelectingMarker.current]: curSelectedMarker,
      });
    }

    if (setCurSelectedMarker) setCurSelectedMarker(null);

    curSelectingMarker.current = null;
    setSelectedTool('transform');
  }, [curSelectedMarker]);

  return (
    <div className='flex self-stretch flex-col'>
      <div
        className='flex w-[338px] p-4 flex-col gap-6'
        style={{
          height: 'calc(100vh - 270px)',
          overflowY: 'auto',
        }}
      >
        <div className='flex flex-col gap-3 self-stretch'>
          <div className='flex items-center gap-2 self-stretch'>
            <span className='font-source text-[14px] font-normal leading-[150%]'>
              {t('productDefine.gridType')}:
            </span>
            <div className='flex items-center justify-center w-6 h-6 rounded-[2px border-[1px] border-gray-2'>
              <img
                src='/icn/grid_white.svg'
                alt='grid'
                className='w-3 h-3'
              />
            </div>
          </div>
          <div className='flex items-center gap-6 self-stretch'>
            <div className='flex gap-2 flex-1 self-stretch items-center'>
              <img
                src='/icn/addRow_white.svg'
                alt='addRow'
                className='w-[14px] h-[14px]'
              />
              <InputNumber
                min={gridColumnCount > 1 ? 1 : 2}
                style={{ width: '120px' }}
                value={gridRowCount}
                onChange={(value) => setGridRowCount(value)}
                precision={0}
              />
            </div>
            <div className='flex items-center gap-2 flex-1 self-stretch'>
              <img
                src='/icn/addCol_white.svg'
                alt='addColumn'
                className='w-[14px] h-[14px]'
              />
              <InputNumber
                min={gridRowCount > 1 ? 1 : 2}
                style={{ width: '120px' }}
                value={gridColumnCount}
                onChange={(value) => setGridColumnCount(value)}
                precision={0}
              />
            </div>
          </div>
        </div>
        <div className='flex flex-col gap-4 self-stretch'>
          <div className='flex flex-col gap-1 self-stretch'>
            <span className='font-source text-[14px] font-normal leading-[150%]'>
              {t('productDefine.defineMarkerPointsOfThree')}
            </span>
            <span className='font-source text-[12px] font-normal leading-[150%]'>
              {t('productDefine.theMarkerPointsOfTheUnit')}
            </span>
          </div>
          <div className='flex items-center gap-2 self-stretch'>
            {_.isEmpty(curMarkers.tl) ?
              <Button
                style={{ width: '284px' }}
                onClick={() => {
                  if (setCurSelectedMarker) setCurSelectedMarker(null);
                  if (setCurMarkers) setCurMarkers(prev => ({ ...prev, tl: undefined }));
                  setSelectedTool('selectMarker');
                  curSelectingMarker.current = 'tl';
                  isCurSelectingPairMode.current = false;
                }}
              >
                <div className='flex items-center gap-2 self-stretch'>
                  <img
                    src='/icn/plus_white.svg'
                    alt='plus'
                    className='w-[10px] h-[10px]'
                  />
                  <span className='font-source text-[12px] font-normal leading-[150%] pt-0.5'>
                    {t('productDefine.pointATopLeftUnit')}
                  </span>
                </div>
              </Button>
            :
              <Fragment>
                <span className='font-source text-[12px] font-normal leading-[150%] pt-0.5 flex-1'>
                  {`(${_.get(curMarkers, 'tl.x', 0)}, ${_.get(curMarkers, 'tl.y', 0)})`}
                </span>
                <div className='flex items-center flex-1 self-stretch'>
                  <Button
                    style={{ width: '100%' }}
                    onClick={() => {
                      if (setCurSelectedMarker) setCurSelectedMarker(null);
                      if (setCurMarkers) setCurMarkers(prev => ({ ...prev, tl: undefined }));
                      setSelectedTool('selectMarker');
                      curSelectingMarker.current = 'tl';
                      isCurSelectingPairMode.current = false;
                    }}
                  >
                    <span className='font-source text-[12px] font-normal leading-[150%] pt-0.5'>
                      {t('common.edit')}
                    </span>
                  </Button>
                </div>
              </Fragment>
            }
            <Tooltip
              title={<span className='font-source text-[12px] font-semibold leading-[150%]'>{t('productDefine.markerPointInfoPointA')}</span>}
              placement='top'
            >
              <div className='flex h-6 w-6 items-center justify-center'>
                <img
                  src='/icn/info_white.svg'
                  alt='info'
                  className='w-3 h-3'
                />
              </div>
            </Tooltip>
          </div>
          {gridRowCount > 1 &&
            <div className='flex items-center gap-2 self-stretch'>
              {_.isEmpty(curMarkers.tr) ?
                <Button
                  style={{ width: '284px' }}
                  onClick={() => {
                    if (setCurSelectedMarker) setCurSelectedMarker(null);
                    if (setCurMarkers) setCurMarkers(prev => ({ ...prev, tr: undefined }));
                    setSelectedTool('selectMarker');
                    curSelectingMarker.current = 'tr';
                    isCurSelectingPairMode.current = false;
                  }}
                >
                  <div className='flex items-center gap-2 self-stretch'>
                    <img
                      src='/icn/plus_white.svg'
                      alt='plus'
                      className='w-[10px] h-[10px]'
                    />
                    <span className='font-source text-[12px] font-normal leading-[150%] pt-0.5'>
                      {t('productDefine.pointBTopRightUnit')}
                    </span>
                  </div>
                </Button>
              :
                <Fragment>
                  <span className='font-source text-[12px] font-normal leading-[150%] pt-0.5 flex-1'>
                    {`(${_.get(curMarkers, 'tr.x', 0)}, ${_.get(curMarkers, 'tr.y', 0)})`}
                  </span>
                  <div className='flex items-center flex-1 self-stretch'>
                    <Button
                      style={{ width: '100%' }}
                      onClick={() => {
                        if (setCurSelectedMarker) setCurSelectedMarker(null);
                        if (setCurMarkers) setCurMarkers(prev => ({ ...prev, tr: undefined }));
                        setSelectedTool('selectMarker');
                        curSelectingMarker.current = 'tr';
                        isCurSelectingPairMode.current = false;
                      }}
                    >
                      <span className='font-source text-[12px] font-normal leading-[150%] pt-0.5'>
                        {t('common.edit')}
                      </span>
                    </Button>
                  </div>
                </Fragment>
              }
              <div className='flex h-6 w-6 items-center justify-center'>
              <Tooltip
                title={<span className='font-source text-[12px] font-semibold leading-[150%]'>{t('productDefine.markerPointInfoPointB')}</span>}
                placement='top'
              >
                <div className='flex h-6 w-6 items-center justify-center'>
                  <img
                    src='/icn/info_white.svg'
                    alt='info'
                    className='w-3 h-3'
                  />
                </div>
              </Tooltip>
              </div>
            </div>
          }
          {gridColumnCount > 1 &&
            <div className='flex items-center gap-2 self-stretch'>
              {_.isEmpty(curMarkers.bl) ?
                <Button
                  style={{ width: '284px' }}
                  onClick={() => {
                    if (setCurSelectedMarker) setCurSelectedMarker(null);
                    if (setCurMarkers) setCurMarkers(prev => ({ ...prev, bl: undefined }));
                    setSelectedTool('selectMarker');
                    curSelectingMarker.current = 'bl';
                    isCurSelectingPairMode.current = false;
                  }}
                >
                  <div className='flex items-center gap-2 self-stretch'>
                    <img
                      src='/icn/plus_white.svg'
                      alt='plus'
                      className='w-[10px] h-[10px]'
                    />
                    <span className='font-source text-[12px] font-normal leading-[150%] pt-0.5'>
                      {t('productDefine.pointCBottomLeftUnit')}
                    </span>
                  </div>
                </Button>
              :
                <Fragment>
                  <span className='font-source text-[12px] font-normal leading-[150%] pt-0.5 flex-1'>
                    {`(${_.get(curMarkers, 'bl.x', 0)}, ${_.get(curMarkers, 'bl.y', 0)})`}
                  </span>
                  <div className='flex items-center flex-1 self-stretch'>
                    <Button
                      style={{ width: '100%' }}
                      onClick={() => {
                        if (setCurSelectedMarker) setCurSelectedMarker(null);
                        if (setCurMarkers) setCurMarkers(prev => ({ ...prev, bl: undefined }));
                        setSelectedTool('selectMarker');
                        curSelectingMarker.current = 'bl';
                        isCurSelectingPairMode.current = false;
                      }}
                    >
                      <span className='font-source text-[12px] font-normal leading-[150%] pt-0.5'>
                        {t('common.edit')}
                      </span>
                    </Button>
                  </div>
                </Fragment>
              }
              <div className='flex h-6 w-6 items-center justify-center'>
                <Tooltip
                title={<span className='font-source text-[12px] font-semibold leading-[150%]'>{t('productDefine.markerPointInfoPointC')}</span>}
                placement='top'
              >
                <div className='flex h-6 w-6 items-center justify-center'>
                  <img
                    src='/icn/info_white.svg'
                    alt='info'
                    className='w-3 h-3'
                  />
                </div>
              </Tooltip>
              </div>
            </div>
          }
        </div>
        <div className='flex items-center gap-2 self-stretch'>
          <Checkbox
            checked={pairMode}
            onChange={() => setPairMode(!pairMode)}
          />
          <div className='flex gap-1 items-center self-stretch'>
            <span className='font-source text-[14px] font-normal leading-[150%] pt-0.5'>
              {t('productDefine.pairMode')}
            </span>
            <Tooltip
              title={<span className='font-source text-[12px] font-semibold leading-[150%]'>{t('productDefine.pairModeDesc')}</span>}
              placement='top'
            >
              <div className='flex h-6 w-6 items-center justify-center pb-0.5'>
                <img
                  src='/icn/info_white.svg'
                  alt='info'
                  className='w-3 h-3'
                />
              </div>
            </Tooltip>
          </div>
        </div>
        {pairMode &&
        <Fragment>
        <div className='flex flex-col gap-3 self-stretch'>
          <div className='flex items-center gap-2 self-stretch'>
            <span className='font-source text-[14px] font-normal leading-[150%]'>
              {t('productDefine.rotationAngle')}:
            </span>
            <InputNumber
              value={pairModeAngle}
              onChange={(value) => setPairModeAngle(value)}
              controls={false}
              min={0}
              max={360}
              precision={2}
              style={{ width: '100px' }}
            />
          </div>
        </div>
        <div className='flex flex-col gap-3 self-stretch'>
          <div className='flex items-center gap-2 self-stretch'>
            <span className='font-source text-[14px] font-normal leading-[150%]'>
              {t('productDefine.pairModeGridType')}:
            </span>
            <div className='flex items-center justify-center w-6 h-6 rounded-[2px border-[1px] border-gray-2'>
              <img
                src='/icn/grid_white.svg'
                alt='grid'
                className='w-3 h-3'
              />
            </div>
          </div>
          <div className='flex items-center gap-6 self-stretch'>
            <div className='flex gap-2 flex-1 self-stretch items-center'>
              <img
                src='/icn/addRow_white.svg'
                alt='addRow'
                className='w-[14px] h-[14px]'
              />
              <InputNumber
                min={pairModeColumnCount > 1 ? 1 : 2}
                style={{ width: '120px' }}
                value={pairModeRowCount}
                onChange={(value) => setPairModeRowCount(value)}
                precision={0}
              />
            </div>
            <div className='flex items-center gap-2 flex-1 self-stretch'>
              <img
                src='/icn/addCol_white.svg'
                alt='addColumn'
                className='w-[14px] h-[14px]'
              />
              <InputNumber
                min={pairModeRowCount > 1 ? 1 : 2}
                style={{ width: '120px' }}
                value={pairModeColumnCount}
                onChange={(value) => setPairModeColumnCount(value)}
                precision={0}
              />
            </div>
          </div>
        </div>
        <div className='flex flex-col gap-4 self-stretch'>
          <div className='flex flex-col gap-1 self-stretch'>
            <span className='font-source text-[14px] font-normal leading-[150%]'>
              {t('productDefine.defineMarkerPointsOfThree')}({t('productDefine.pleaseUseTheSamePointDefinedAbove')})
            </span>
            <span className='font-source text-[12px] font-normal leading-[150%]'>
              {t('productDefine.theMarkerPointsOfTheUnit')}
            </span>
          </div>
          <div className='flex items-center gap-2 self-stretch'>
            {_.isEmpty(curPairModeMarkers.tl) ?
              <Button
                style={{ width: '284px' }}
                onClick={() => {
                  if (setCurSelectedMarker) setCurSelectedMarker(null);
                  if (setCurPairModeMarkers) setCurPairModeMarkers(prev => ({ ...prev, tl: undefined }));
                  setSelectedTool('selectMarker');
                  curSelectingMarker.current = 'tl';
                  isCurSelectingPairMode.current = true;
                }}
              >
                <div className='flex items-center gap-2 self-stretch'>
                  <img
                    src='/icn/plus_white.svg'
                    alt='plus'
                    className='w-[10px] h-[10px]'
                  />
                  <span className='font-source text-[12px] font-normal leading-[150%] pt-0.5'>
                    {t('productDefine.pointATopLeftUnit')}
                  </span>
                </div>
              </Button>
            :
              <Fragment>
                <span className='font-source text-[12px] font-normal leading-[150%] pt-0.5 flex-1'>
                  {`(${_.get(curPairModeMarkers, 'tl.x', 0)}, ${_.get(curPairModeMarkers, 'tl.y', 0)})`}
                </span>
                <div className='flex items-center flex-1 self-stretch'>
                  <Button
                    style={{ width: '100%' }}
                    onClick={() => {
                      if (setCurSelectedMarker) setCurSelectedMarker(null);
                      if (setCurPairModeMarkers) setCurPairModeMarkers(prev => ({ ...prev, tl: undefined }));
                      setSelectedTool('selectMarker');
                      curSelectingMarker.current = 'tl';
                      isCurSelectingPairMode.current = true;
                    }}
                  >
                    <span className='font-source text-[12px] font-normal leading-[150%] pt-0.5'>
                      {t('common.edit')}
                    </span>
                  </Button>
                </div>
              </Fragment>
            }
            <Tooltip
              title={<span className='font-source text-[12px] font-semibold leading-[150%]'>{t('productDefine.markerPointInfoPointA')}</span>}
              placement='top'
            >
              <div className='flex h-6 w-6 items-center justify-center'>
                <img
                  src='/icn/info_white.svg'
                  alt='info'
                  className='w-3 h-3'
                />
              </div>
            </Tooltip>
          </div>
          {pairModeRowCount > 1 &&
            <div className='flex items-center gap-2 self-stretch'>
              {_.isEmpty(curPairModeMarkers.tr) ?
                <Button
                  style={{ width: '284px' }}
                  onClick={() => {
                    if (setCurSelectedMarker) setCurSelectedMarker(null);
                    if (setCurPairModeMarkers) setCurPairModeMarkers(prev => ({ ...prev, tr: undefined }));
                    setSelectedTool('selectMarker');
                    curSelectingMarker.current = 'tr';
                    isCurSelectingPairMode.current = true;
                  }}
                >
                  <div className='flex items-center gap-2 self-stretch'>
                    <img
                      src='/icn/plus_white.svg'
                      alt='plus'
                      className='w-[10px] h-[10px]'
                    />
                    <span className='font-source text-[12px] font-normal leading-[150%] pt-0.5'>
                      {t('productDefine.pointBTopRightUnit')}
                    </span>
                  </div>
                </Button>
              :
                <Fragment>
                  <span className='font-source text-[12px] font-normal leading-[150%] pt-0.5 flex-1'>
                    {`(${_.get(curPairModeMarkers, 'tr.x', 0)}, ${_.get(curPairModeMarkers, 'tr.y', 0)})`}
                  </span>
                  <div className='flex items-center flex-1 self-stretch'>
                    <Button
                      style={{ width: '100%' }}
                      onClick={() => {
                        if (setCurSelectedMarker) setCurSelectedMarker(null);
                        if (setCurPairModeMarkers) setCurPairModeMarkers(prev => ({ ...prev, tr: undefined }));
                        setSelectedTool('selectMarker');
                        curSelectingMarker.current = 'tr';
                        isCurSelectingPairMode.current = true;
                      }}
                    >
                      <span className='font-source text-[12px] font-normal leading-[150%] pt-0.5'>
                        {t('common.edit')}
                      </span>
                    </Button>
                  </div>
                </Fragment>
              }
              <div className='flex h-6 w-6 items-center justify-center'>
              <Tooltip
                title={<span className='font-source text-[12px] font-semibold leading-[150%]'>{t('productDefine.markerPointInfoPointB')}</span>}
                placement='top'
              >
                <div className='flex h-6 w-6 items-center justify-center'>
                  <img
                    src='/icn/info_white.svg'
                    alt='info'
                    className='w-3 h-3'
                  />
                </div>
              </Tooltip>
              </div>
            </div>
          }
          {pairModeColumnCount > 1 &&
            <div className='flex items-center gap-2 self-stretch'>
              {_.isEmpty(curPairModeMarkers.bl) ?
                <Button
                  style={{ width: '284px' }}
                  onClick={() => {
                    if (setCurSelectedMarker) setCurSelectedMarker(null);
                    if (setCurPairModeMarkers) setCurPairModeMarkers(prev => ({ ...prev, bl: undefined }));
                    setSelectedTool('selectMarker');
                    curSelectingMarker.current = 'bl';
                    isCurSelectingPairMode.current = true;
                  }}
                >
                  <div className='flex items-center gap-2 self-stretch'>
                    <img
                      src='/icn/plus_white.svg'
                      alt='plus'
                      className='w-[10px] h-[10px]'
                    />
                    <span className='font-source text-[12px] font-normal leading-[150%] pt-0.5'>
                      {t('productDefine.pointCBottomLeftUnit')}
                    </span>
                  </div>
                </Button>
              :
                <Fragment>
                  <span className='font-source text-[12px] font-normal leading-[150%] pt-0.5 flex-1'>
                    {`(${_.get(curPairModeMarkers, 'bl.x', 0)}, ${_.get(curPairModeMarkers, 'bl.y', 0)})`}
                  </span>
                  <div className='flex items-center flex-1 self-stretch'>
                    <Button
                      style={{ width: '100%' }}
                      onClick={() => {
                        if (setCurSelectedMarker) setCurSelectedMarker(null);
                        if (setCurPairModeMarkers) setCurPairModeMarkers(prev => ({ ...prev, bl: undefined }));
                        setSelectedTool('selectMarker');
                        curSelectingMarker.current = 'bl';
                        isCurSelectingPairMode.current = true;
                      }}
                    >
                      <span className='font-source text-[12px] font-normal leading-[150%] pt-0.5'>
                        {t('common.edit')}
                      </span>
                    </Button>
                  </div>
                </Fragment>
              }
              <div className='flex h-6 w-6 items-center justify-center'>
                <Tooltip
                title={<span className='font-source text-[12px] font-semibold leading-[150%]'>{t('productDefine.markerPointInfoPointC')}</span>}
                placement='top'
              >
                <div className='flex h-6 w-6 items-center justify-center'>
                  <img
                    src='/icn/info_white.svg'
                    alt='info'
                    className='w-3 h-3'
                  />
                </div>
              </Tooltip>
              </div>
            </div>
          }
        </div>
        </Fragment>
        }
        <div className='flex items-center gap-2 self-stretch'>
          <Checkbox
            checked={isSubBoardSelectionRoiDisplayed}
            onChange={(e) => {
              setIsSubBoardSelectionRoiDisplayed(e.target.checked);
            }}
          />
          <span className='font-source text-[12px] font-normal leading-[150%] pt-0.5'>
            {t('productDefine.previewArray')}
          </span>
        </div>
      </div>
      <div className='flex items-center p-4 gap-2 self-stretch'>
        <Button
          style={{ width: '50%' }}
          onClick={() => {
            // also clear all markers
            if (setCurMarkers) setCurMarkers({});
            if (setCurPairModeMarkers) setCurPairModeMarkers({});
            // also clear preview array transforms except the first one
            setPreviewArrayTransforms([_.get(arrayRegisteration, 'array_transforms[0]', {})]);
            setActiveTab('arrayGroup');
          }}
        >
          <span className='font-source text-[12px] font-semibold leading-[normal]'>
            {t('common.cancel')}
          </span>
        </Button>
        <Button
          type='primary'
          style={{ width: '50%' }}
          onClick={() => {
            handleGenerateArraySubmit(
              arrayRegisteration,
              gridRowCount,
              gridColumnCount,
              curMarkers,
              curProduct,
              0,
              handleRefetchAllComponents,
              refetchArrayRegisteration,
              pairMode,
              pairModeAngle,
              pairModeRowCount,
              pairModeColumnCount,
              curPairModeMarkers,
            );
          }}
        >
          <span className='font-source text-[12px] font-semibold leading-[normal]'>
            {t('productDefine.createArray')}
          </span>
        </Button>
      </div>
    </div>
  );
};

export default SmartPCBArray;