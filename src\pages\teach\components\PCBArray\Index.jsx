import { But<PERSON>, Config<PERSON><PERSON>ider, Ta<PERSON>, Tooltip } from 'antd';
import React, { useEffect, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import ArrayGroup from './ArrayGroup';
import SmartPCBArray from './SmartPCBArray';
import Display from './Display';
import { useGetArrayRegisterationQuery, useGoldenRegisterArrayMutation, useLazyGetProductComponentQuery, useLazyOptimizeArrayGroupQuery, useLazyGetAllFeaturesQuery } from '../../../../services/product';
import _ from 'lodash';
import { ALERT_TYPES, aoiAlert } from '../../../../common/alert';
import { rotatePoint } from '../../../../viewer/util';
import { useDispatch } from 'react-redux';
import { setContainerLvlLoadingMsg, setIsContainerLvlLoadingEnabled } from '../../../../reducer/setting';
import { optimizeArrayGroupRetryCount } from '../../../../common/const';


const PCBArray = (props) => {
  const {
    curProduct,
    refetchCurProduct,
    setComponentsActiveTab,
    arrayRegisteration,
    refetchArrayRegisteration,
  } = props;

  const dispatch = useDispatch();

  const { t } = useTranslation();

  const reftFormColRef = useRef(null);

  const [activeTab, setActiveTab] = useState('arrayGroup');
  const [allComponents, setAllComponents] = useState([]);
  const [allFeatures, setAllFeatures] = useState([]);
  const [selectedTool, setSelectedTool] = useState('transform');
  const [leftColHeight, setLeftColHeight] = useState(0);
  const [curSelectedMarker, setCurSelectedMarker] = useState(null);
  const [curMarkers, setCurMarkers] = useState({});
  const [curPairModeMarkers, setCurPairModeMarkers] = useState({});
  const [isSubBoardSelectionRoiDisplayed, setIsSubBoardSelectionRoiDisplayed] = useState(true);
  const [previewArrayTransforms, setPreviewArrayTransforms] = useState(null);
  const [previewComponents, setPreviewComponents] = useState(null);
  const [previewFeatures, setPreviewFeatures] = useState(null);
  const [panZoomToArrayBoardSelectionIdx, setPanZoomToArrayBoardSelectionIdx] = useState(null);

  // const { data: arrayRegisteration, refetch: refetchArrayRegisteration } = useGetArrayRegisterationQuery({ product_id: Number(_.get(curProduct, 'product_id', 0)), step: 0 });
  const [lazyGetAllComponents] = useLazyGetProductComponentQuery();
  const [lazyGetAllFeatures] = useLazyGetAllFeaturesQuery();
  const [registerGoldenArray] = useGoldenRegisterArrayMutation();
  const [lazyOptimizeArrayGroup] = useLazyOptimizeArrayGroupQuery();

  const handleRefetchAllComponents = async (productId, step) => {
    const res = await lazyGetAllComponents({ definition_product_id: productId, definition_step: step }, false);

    if (res.error) {
      console.error('getProductComponent error:', _.get(res, 'error.message', ''));
      return;
    }

    setAllComponents(res.data);
  };

  const handleRefetchAllFeatures = async (productId, step) => {
    const res = await lazyGetAllFeatures({
      product_id: productId,
      step: step,
      marker: false,
    }, false);

    if (res.error) {
      console.error('getAllFeatures error:', _.get(res, 'error.message', ''));
      return;
    }

    setAllFeatures(res.data || []);
  };

  const handleAutoAdjustArrayGroup = async (arrayRegisteration, curProduct) => {
    dispatch(setIsContainerLvlLoadingEnabled(true));
    dispatch(setContainerLvlLoadingMsg(t('loader.autoAdjustArrayGroup')));

    for (let i = 0; i < optimizeArrayGroupRetryCount; i++) {
      const res = await lazyOptimizeArrayGroup({
        product_id: Number(_.get(curProduct, 'product_id', 0)),
        step: 0,
      });

      if (res.error) {
        aoiAlert(t('notification.error.autoAdjustArrayGroup'), ALERT_TYPES.COMMON_ERROR);
        console.error('optimizeArrayGroup error:', _.get(res, 'error.message', ''));
        dispatch(setIsContainerLvlLoadingEnabled(false));
        dispatch(setContainerLvlLoadingMsg(''));
        return;
      }

      const res1 = await registerGoldenArray({
        product_id: Number(_.get(curProduct, 'product_id', 0)),
        step: 0,
        array_transforms: res.data,
        selection_roi: arrayRegisteration.selection_roi,
        component_ids: arrayRegisteration.component_ids,
      });

      if (res1.error) {
        aoiAlert(t('notification.error.registerGoldenArray'), ALERT_TYPES.COMMON_ERROR);
        console.error('registerGoldenArray error:', _.get(res1, 'error.message', ''));
        dispatch(setIsContainerLvlLoadingEnabled(false));
        dispatch(setContainerLvlLoadingMsg(''));
        return;
      }
    }

    await refetchArrayRegisteration();
    await handleRefetchAllComponents(Number(_.get(curProduct, 'product_id', 0)), 0);
    await handleRefetchAllFeatures(Number(_.get(curProduct, 'product_id', 0)), 0);
    dispatch(setIsContainerLvlLoadingEnabled(false));
    dispatch(setContainerLvlLoadingMsg(''));
  };

  const handleFinishDrawingSelectionRoi = async (filteredComponents, productId, step, arrayTransform, selectionRoi) => {
    const res = await registerGoldenArray({
      product_id: productId,
      step: step,
      array_transforms: arrayTransform,
      selection_roi: selectionRoi,
      component_ids: _.map(filteredComponents, c => c.region_group_id),
    });

    if (res.error) {
      console.error('registerGoldenArray error:', _.get(res, 'error.message', ''));
      aoiAlert(t('notification.error.registerGoldenArray'), ALERT_TYPES.COMMON_ERROR);
      return;
    }

    await refetchArrayRegisteration();
    await handleRefetchAllComponents(productId, step);
    await handleRefetchAllFeatures(productId, step);
    setPreviewArrayTransforms(null);
    setPreviewComponents(null);
  };

  useEffect(() => {
    if (_.isUndefined(curProduct) || !curProduct) return;
    handleRefetchAllComponents(Number(_.get(curProduct, 'product_id', 0)), 0);
    handleRefetchAllFeatures(Number(_.get(curProduct, 'product_id', 0)), 0);
    setLeftColHeight(reftFormColRef.current.offsetHeight);
  }, []);

  useEffect(() => {
    if (!previewArrayTransforms) {
      setPreviewComponents(null);
      setPreviewFeatures(null);
      return;
    }

    const baseComponents = allComponents.filter(c => _.get(c, 'array_index', 0) === 0);
    if (baseComponents.length === 0) {
      setPreviewComponents(null);
      setPreviewFeatures(null);
      return;
    }

    // Get base component IDs
    const baseComponentIds = _.map(baseComponents, 'region_group_id');

    // Filter features that belong to base components (array_index 0)
    const baseFeatures = allFeatures.filter(f =>
      _.get(f, 'array_index', 0) === 0 &&
      _.includes(baseComponentIds, f.group_id)
    );

    // const newComponents = [...allComponents];
    const newComponents = [...baseComponents];
    const newFeatures = [...baseFeatures];

    for (const transform of previewArrayTransforms) {
      if (transform.array_index === 0) continue;

      for (const comp of baseComponents) {
        const pMin = _.get(comp, 'shape.points[0]', { x: 0, y: 0 });
        const pMax = _.get(comp, 'shape.points[1]', { x: 0, y: 0 });
        let transformedPMin = {
          x: pMin.x + _.get(transform, 'translation.x', 0),
          y: pMin.y + _.get(transform, 'translation.y', 0),
        };
        let transformedPMax = {
          x: pMax.x + _.get(transform, 'translation.x', 0),
          y: pMax.y + _.get(transform, 'translation.y', 0),
        };

        if (_.get(arrayRegisteration, 'array_transforms', []).length === 1 && previewArrayTransforms.length > 1) {
          // this means we need to apply the array board's transform to the component's shape
          transformedPMin = rotatePoint(transformedPMin, transform.rotation.angle, transform.rotation.center);
          transformedPMax = rotatePoint(transformedPMax, transform.rotation.angle, transform.rotation.center);
        }

        newComponents.push({
          ..._.cloneDeep(comp),
          array_index: transform.array_index,
          shape: {
            ..._.get(comp, 'shape', {}),
            points: [transformedPMin, transformedPMax],
            angle: _.get(comp, 'shape.angle', 0) + _.get(transform, 'rotation.angle', 0),
          }
        });
      }

      // Transform features for this array index
      for (const feature of baseFeatures) {
        const featurePMin = _.get(feature, 'roi.points[0]', { x: 0, y: 0 });
        const featurePMax = _.get(feature, 'roi.points[1]', { x: 0, y: 0 });
        let transformedFeaturePMin = {
          x: featurePMin.x + _.get(transform, 'translation.x', 0),
          y: featurePMin.y + _.get(transform, 'translation.y', 0),
        };
        let transformedFeaturePMax = {
          x: featurePMax.x + _.get(transform, 'translation.x', 0),
          y: featurePMax.y + _.get(transform, 'translation.y', 0),
        };

        if (_.get(arrayRegisteration, 'array_transforms', []).length === 1 && previewArrayTransforms.length > 1) {
          // this means we need to apply the array board's transform to the feature's roi
          transformedFeaturePMin = rotatePoint(transformedFeaturePMin, transform.rotation.angle, transform.rotation.center);
          transformedFeaturePMax = rotatePoint(transformedFeaturePMax, transform.rotation.angle, transform.rotation.center);
        }

        newFeatures.push({
          ..._.cloneDeep(feature),
          array_index: transform.array_index,
          roi: {
            ..._.get(feature, 'roi', {}),
            points: [transformedFeaturePMin, transformedFeaturePMax],
            angle: _.get(feature, 'roi.angle', 0) + _.get(transform, 'rotation.angle', 0),
          }
        });
      }
    }

    setPreviewComponents(newComponents);
    setPreviewFeatures(newFeatures);
  }, [previewArrayTransforms, allComponents, allFeatures]);

  const arrayRegisterationForDisplay = previewArrayTransforms ?
    { ...arrayRegisteration, array_transforms: previewArrayTransforms } :
    arrayRegisteration;

  const allComponentsForDisplay = previewComponents || allComponents;
  const allFeaturesForDisplay = previewFeatures || allFeatures;

  // useEffect(() => {
  //   if (activeTab === 'smartPCBArray') {
  //     setIsSubBoardSelectionRoiDisplayed(true);
  //   } else {
  //     setIsSubBoardSelectionRoiDisplayed(false);
  //   }
  // }, [activeTab]);

  return (
    <div className='flex flex-1 gap-0.5 self-stretch rounded-[6px]'>
      <div className='flex w-[348px] flex-col self-stretch'>
        <div className='flex pt-1 items-center self-stretch border-t-[1px] border-t-gray-2'>
          <ConfigProvider
            theme={{
              components: {
                Tabs: {
                  cardPadding: '1px 12px',
                  horizontalMargin: '4px 4px 0 4px',
                },
              }
            }}
          >
            <Tabs
              activeKey={activeTab}
              style={{ width: '100%' }}
              onChange={(key) => {
                setActiveTab(key);
              }}
              type='card'
              items={[
                {
                  label: <div className='flex py-2 gap-2 items-center justify-center'>
                    <span className={`font-source text-[14px] font-${activeTab === 'arrayGroup' ? 'semibold' : 'normal'} leading-[normal]`}>
                      {t('productDefine.arrayGroup')}
                    </span>
                  </div>,
                  key: 'arrayGroup',
                },
                {
                  label: <div className='flex py-2 gap-2 items-center justify-center'>
                    <span className={`font-source text-[14px] font-${activeTab === 'smartPCBArray' ? 'semibold' : 'normal'} leading-[normal]`}>
                      {t('productDefine.smartPCBArray')}
                    </span>
                  </div>,
                  key: 'smartPCBArray',
                  disabled: _.get(arrayRegisteration, 'array_transforms', []).length === 0 || _.isEmpty(_.get(arrayRegisteration, 'selection_roi', {})),
                }
              ]}
            />
          </ConfigProvider>
          <Button
            onClick={() => {
              handleAutoAdjustArrayGroup(arrayRegisteration, curProduct);
            }}
          >
            <span className='font-source text-[12px] font-normal leading-[150%]'>
              {t('productDefine.autoAdjustArrayGroup')}
            </span>
          </Button>
        </div>
        <div
          className='flex flex-col flex-1 self-stretch px-2 gap-2'
          ref={reftFormColRef}
        >
          {activeTab === 'arrayGroup' &&
            // manual mode
            <ArrayGroup
              arrayRegisteration={arrayRegisteration}
              setSelectedTool={setSelectedTool}
              leftColHeight={leftColHeight}
              curProduct={curProduct}
              refetchArrayRegisteration={refetchArrayRegisteration}
              handleRefetchAllComponents={handleRefetchAllComponents}
              handleRefetchAllFeatures={handleRefetchAllFeatures}
              setActiveTab={setActiveTab}
              setPanZoomToArrayBoardSelectionIdx={setPanZoomToArrayBoardSelectionIdx}
            />
          }
          {activeTab === 'smartPCBArray' &&
            <SmartPCBArray
              arrayRegisteration={arrayRegisteration}
              setActiveTab={setActiveTab}
              curSelectedMarker={curSelectedMarker}
              setCurSelectedMarker={setCurSelectedMarker}
              curMarkers={curMarkers}
              setCurMarkers={setCurMarkers}
              setSelectedTool={setSelectedTool}
              curProduct={curProduct}
              refetchArrayRegisteration={refetchArrayRegisteration}
              handleRefetchAllComponents={handleRefetchAllComponents}
              isSubBoardSelectionRoiDisplayed={isSubBoardSelectionRoiDisplayed}
              setIsSubBoardSelectionRoiDisplayed={setIsSubBoardSelectionRoiDisplayed}
              setPreviewArrayTransforms={setPreviewArrayTransforms}
              setComponentsActiveTab={setComponentsActiveTab}
              curPairModeMarkers={curPairModeMarkers}
              setCurPairModeMarkers={setCurPairModeMarkers}
            />
          }
        </div>
      </div>
      <div className='flex flex-1 self-stretch bg-[#000000cc]'>
        <Display
          curProduct={curProduct}
          allComponents={allComponentsForDisplay}
          allFeatures={allFeaturesForDisplay}
          selectedTool={selectedTool}
          setSelectedTool={setSelectedTool}
          handleFinishDrawingSelectionRoi={handleFinishDrawingSelectionRoi}
          arrayRegisteration={arrayRegisterationForDisplay}
          handleRefetchAllComponents={handleRefetchAllComponents}
          handleRefetchAllFeatures={handleRefetchAllFeatures}
          refetchArrayRegisteration={refetchArrayRegisteration}
          setCurSelectedMarker={setCurSelectedMarker}
          curSelectedMarker={curSelectedMarker}
          curMarkers={curMarkers}
          curPairModeMarkers={curPairModeMarkers}
          isSubBoardSelectionRoiDisplayed={isSubBoardSelectionRoiDisplayed}
          panZoomToArrayBoardSelectionIdx={panZoomToArrayBoardSelectionIdx}
          setPanZoomToArrayBoardSelectionIdx={setPanZoomToArrayBoardSelectionIdx}
        />
      </div>
    </div>
  );
};

export default PCBArray;