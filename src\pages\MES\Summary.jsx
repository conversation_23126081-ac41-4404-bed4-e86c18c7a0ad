import { Fragment, useEffect, useRef, useState } from "react";
import { useTranslation } from "react-i18next";
import { Bar, Bar<PERSON>hart, Cell, Pie, PieChart, ResponsiveContainer, XAxis, YAxis, CartesianGrid, Tooltip } from "recharts";
import _ from 'lodash';
import { useGetInspectionSummaryQuery } from "../../services/inference";
import { toLocalISOString } from "../../common/util";


const Summary = (props) => {
  const {
    selectedGoldenProdId,
    startTime,
    endTime,
    snQuery,
  } = props;

  const { t } = useTranslation();

  const summaryContainer = useRef(null);
  const pieChartContainer = useRef(null);
  const barChartContainer = useRef(null);
  const allSectionsContainer = useRef(null);
  const [activePrintSection, setActivePrintSection] = useState(null);

  const [boardNGPassRatioData, setBoardNGPassRatioData] = useState([
    { name: t('common.pass'), value: 0, color: '#27AE60' },
    { name: t('common.ng'), value: 0, color: '#E74C3C' }
  ]);
  const [topTenNGComponentData, setTopTenNGComponentData] = useState([]);

  const { data: inspectionSummary } = useGetInspectionSummaryQuery({
    golden_product_id: _.isInteger(selectedGoldenProdId) ? Number(selectedGoldenProdId) : undefined,
    serial_no: _.isEmpty(snQuery) ? undefined : snQuery,
    start_datetime: _.isNull(startTime) ? undefined : toLocalISOString(new Date(startTime)),
    end_datetime: _.isNull(endTime) ? undefined : toLocalISOString(new Date(endTime)),
  }, {
    refetchOnMountOrArgChange: true,
  });

  useEffect(() => {
    setBoardNGPassRatioData([
      {
        name: t('common.pass'),
        value: _.get(inspectionSummary, 'passed_product', 0),
        color: '#27AE60'
      },
      {
        name: t('common.ng'),
        value: _.get(inspectionSummary, 'inspected_product', 0) - _.get(inspectionSummary, 'passed_product', 0),
        color: '#E74C3C'
      }
    ]);

    // component_false_defect_map
    // ex. { component_1: 10, component_2: 20, ... }

    setTopTenNGComponentData(
      _.orderBy(_.map(_.get(inspectionSummary, 'component_false_defect_map' , {}), (value, key) => ({
        [t('common.name')]: key,
        [t('common.amount')]: value
      })), [t('common.amount')], 'desc')
    );
  }, [inspectionSummary]);

  // Custom Y-axis tick component with ellipsis
  const CustomYAxisTick = ({ x, y, payload }) => {
    const maxLength = 12; // Maximum characters to display
    const text = payload.value;
    const displayText = text.length > maxLength ? `${text.substring(0, maxLength)}...` : text;

    return (
      <g transform={`translate(${x},${y})`}>
        <title>{text}</title>
        <text
          x={0}
          y={0}
          dy={4}
          textAnchor="end"
          fill="#FFFFFF"
          fontSize="12px"
        >
          <title>{text}</title>
          {displayText}
        </text>
      </g>
    );
  };

  // Handle mouse enter/leave for print sections
  const handleSectionMouseEnter = (section) => {
    setActivePrintSection(section);
  };

  const handleSectionMouseLeave = () => {
    setActivePrintSection(null);
  };

  // Hide tooltips before printing and handle print mode
  useEffect(() => {
    const handleBeforePrint = () => {
      // Move mouse away to hide any active tooltips
      const event = new MouseEvent('mousemove', {
        clientX: -1000,
        clientY: -1000,
      });
      document.dispatchEvent(event);

      // If no specific section is active, enable print-all mode
      if (!activePrintSection && allSectionsContainer.current) {
        allSectionsContainer.current.classList.add('print-all-sections');
      }
    };

    const handleAfterPrint = () => {
      // Remove print-all mode after printing
      if (allSectionsContainer.current) {
        allSectionsContainer.current.classList.remove('print-all-sections');
      }
    };

    window.addEventListener('beforeprint', handleBeforePrint);
    window.addEventListener('afterprint', handleAfterPrint);
    return () => {
      window.removeEventListener('beforeprint', handleBeforePrint);
      window.removeEventListener('afterprint', handleAfterPrint);
    };
  }, [activePrintSection]);

  return (
    <Fragment>
      <style>{`
        /* Preserve colors in print */
        .printable-overview,
        .printable-piechart,
        .printable-barchart {
          -webkit-print-color-adjust: exact !important;
          print-color-adjust: exact !important;
          color-adjust: exact !important;
        }

        @media print {
          body * {
            visibility: hidden;
          }

          /* Print all sections mode */
          .print-all-sections.all-sections-container,
          .print-all-sections.all-sections-container * {
            visibility: visible;
          }
          .print-all-sections.all-sections-container {
            display: flex !important;
            flex-direction: column !important;
            position: relative;
            gap: 0 !important;
            padding: 0 !important;
          }
          .print-all-sections .section-wrapper {
            page-break-inside: avoid;
            display: flex !important;
            flex-direction: column !important;
            justify-content: center !important;
            align-items: center !important;
            width: 100% !important;
            height: 100vh !important;
            position: relative;
            padding: 2rem !important;
            box-sizing: border-box;
            margin: 0 !important;
          }
          /* Only add page break after first two sections, not the last */
          .print-all-sections .overview-section,
          .print-all-sections .piechart-section {
            page-break-after: always;
          }
          .print-all-sections .barchart-section {
            background: #FFFFFF !important;
          }
          .print-all-sections .overview-section,
          .print-all-sections .piechart-section {
            background: #1a1a1a !important;
          }
          /* Hide empty bottom spacer in print-all mode */
          .bottom-spacer {
            display: none !important;
          }

          /* Print individual section mode */
          .print-active-overview.printable-overview,
          .print-active-overview.printable-overview * {
            visibility: visible;
          }
          .print-active-piechart.printable-piechart,
          .print-active-piechart.printable-piechart * {
            visibility: visible;
          }
          .print-active-barchart.printable-barchart,
          .print-active-barchart.printable-barchart * {
            visibility: visible;
          }
          .printable-overview,
          .printable-piechart,
          .printable-barchart {
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
            background: #1a1a1a !important;
          }

          /* Hide tooltips during print */
          .recharts-tooltip-wrapper {
            display: none !important;
            visibility: hidden !important;
          }
          /* Ensure Y-axis labels are visible */
          .print-active-barchart .recharts-cartesian-axis-tick text,
          .print-all-sections .printable-barchart .recharts-cartesian-axis-tick text {
            fill: #000000 !important;
          }
          /* Ensure X-axis labels are visible */
          .print-active-barchart .recharts-xAxis .recharts-cartesian-axis-tick text,
          .print-all-sections .printable-barchart .recharts-xAxis .recharts-cartesian-axis-tick text {
            fill: #000000 !important;
          }
          /* White background for better readability */
          .print-active-barchart,
          .print-all-sections .printable-barchart {
            background: #FFFFFF !important;
          }
          /* Ensure grid lines are visible */
          .print-active-barchart .recharts-cartesian-grid line,
          .print-all-sections .printable-barchart .recharts-cartesian-grid line {
            stroke: #E0E0E0 !important;
          }
        }
      `}</style>
      <div
        className='all-sections-container flex items-start gap-1 self-stretch px-2 py-1 flex-1'
        ref={allSectionsContainer}
      >
        <div className="section-wrapper overview-section flex flex-col self-stretch flex-1">
          <div className="flex items-start gap-4 flex-[1_0_0] self-stretch rounded border-[color:var(--default-Gray-2,#4F4F4F)] border-0 border-solid">
            <div className="flex flex-col items-start gap-4 flex-[1_0_0] self-stretch [background:rgba(255,255,255,0.03)] px-3 py-4 rounded-sm">
              <div className="flex flex-col items-center gap-1 self-stretch">
                <div className="flex justify-center items-center gap-4 self-stretch px-2 py-0">
                  <span className='text-center font-source text-base font-normal leading-[150%]'>
                    {t('common.overview')}
                  </span>
                </div>
              </div>
              <div
                className={`printable-overview ${activePrintSection === 'overview' ? 'print-active-overview' : ''} flex flex-col items-start gap-1 self-stretch cursor-pointer`}
                ref={summaryContainer}
                onMouseEnter={() => handleSectionMouseEnter('overview')}
                onMouseLeave={handleSectionMouseLeave}
              >
                <div className="flex flex-col items-start gap-1 self-stretch">
                  <div className="flex flex-col items-start gap-4 self-stretch">
                    <div className="flex flex-col items-start gap-1 self-stretch">
                      <div className="flex items-center gap-6 self-stretch border-b-[color:var(--default-Gray-2,#4F4F4F)] [background:rgba(255,255,255,0.06)] px-2 py-0 border-b border-solid">
                        <div className="flex items-start gap-0.5 flex-[1_0_0]">
                          <div className="flex flex-col justify-center items-start flex-[1_0_0] px-0 py-1 rounded-[2px_2px_0_0]">
                            <span className="font-source text-sm font-semibold leading-[150%]">
                              {t('common.PCBA')}
                            </span>
                          </div>
                          <div className="flex flex-col justify-center items-start flex-[1_0_0] px-0 py-1 rounded-[2px_2px_0_0]">
                            <span className="font-source text-sm font-semibold leading-[150%]">
                              {_.get(inspectionSummary, 'inspected_product', 0)}
                            </span>
                          </div>
                          <div className="flex flex-col justify-center items-end flex-[1_0_0] px-0 py-1 rounded-[2px_2px_0_0]">
                            <span className="font-source text-sm font-semibold leading-[150%]">
                              %
                            </span>
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center gap-6 self-stretch px-2 py-0">
                        <div className="flex items-start gap-0.5 flex-[1_0_0]">
                          <div className="flex flex-col justify-center items-start flex-[1_0_0] px-0 py-1 rounded-[2px_2px_0_0]">
                            <span className="font-source text-sm font-normal leading-[150%]">
                              {t('dataAnalysisReport.ok')}
                            </span>
                          </div>
                          <div className="flex flex-col justify-center items-start flex-[1_0_0] px-0 py-1 rounded-[2px_2px_0_0]">
                            <span className="font-source text-sm font-normal leading-[150%]">
                              {_.get(inspectionSummary, 'passed_product', 0)}
                            </span>
                          </div>
                          <div className="flex flex-col justify-center items-end flex-[1_0_0] px-0 py-1 rounded-[2px_2px_0_0]">
                            <span className="font-source text-sm font-normal leading-[150%]">
                              {_.round(_.get(inspectionSummary, 'passed_product', 0)/_.get(inspectionSummary, 'inspected_product', 0) * 100, 2)}%
                            </span>
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center gap-6 self-stretch px-2 py-0">
                        <div className="flex items-start gap-0.5 flex-[1_0_0]">
                          <div className="flex flex-col justify-center items-start flex-[1_0_0] px-0 py-1 rounded-[2px_2px_0_0]">
                            <span className="font-source text-sm font-normal leading-[150%]">
                              {t('dataAnalysisReport.ng')}
                            </span>
                          </div>
                          <div className="flex flex-col justify-center items-start flex-[1_0_0] px-0 py-1 rounded-[2px_2px_0_0]">
                            <span className="font-source text-sm font-normal leading-[150%]">
                              {_.get(inspectionSummary, 'inspected_product', 0) - _.get(inspectionSummary, 'passed_product', 0)}
                            </span>
                          </div>
                          <div className="flex flex-col justify-center items-end flex-[1_0_0] px-0 py-1 rounded-[2px_2px_0_0]">
                            <span className="font-source text-sm font-normal leading-[150%]">
                              {_.round((_.get(inspectionSummary, 'inspected_product', 0) - _.get(inspectionSummary, 'passed_product', 0)) / _.get(inspectionSummary, 'inspected_product', 0) * 100, 2)}%
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className="flex flex-col items-start gap-1 self-stretch">
                      <div className="flex items-center gap-6 self-stretch border-b-[color:var(--default-Gray-2,#4F4F4F)] [background:rgba(255,255,255,0.06)] px-2 py-0 border-b border-solid">
                        <div className="flex items-start gap-0.5 flex-[1_0_0]">
                          <div className="flex flex-col justify-center items-start flex-[1_0_0] px-0 py-1 rounded-[2px_2px_0_0]">
                            <span className="font-source text-sm font-semibold leading-[150%]">
                              {t('dataAnalysisReport.alertedComponent')}
                            </span>
                          </div>
                          <div className="flex flex-col justify-center items-start flex-[1_0_0] px-0 py-1 rounded-[2px_2px_0_0]">
                            <span className="font-source text-sm font-semibold leading-[150%]">
                              {_.get(inspectionSummary, 'false_defect_count', 0) + _.get(inspectionSummary, 'feedback_correct_component', 0)}
                            </span>
                          </div>
                          <div className="flex flex-col justify-center items-end flex-[1_0_0] px-0 py-1 rounded-[2px_2px_0_0]">
                            <span className="font-source text-sm font-semibold leading-[150%]">
                              PPM
                            </span>
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center gap-6 self-stretch px-2 py-0">
                        <div className="flex items-start gap-0.5 flex-[1_0_0]">
                          <div className="flex flex-col justify-center items-start flex-[1_0_0] px-0 py-1 rounded-[2px_2px_0_0]">
                            <span className="font-source text-sm font-normal leading-[150%]">
                              {t('dataAnalysisReport.ng')}
                            </span>
                          </div>
                          <div className="flex flex-col justify-center items-start flex-[1_0_0] px-0 py-1 rounded-[2px_2px_0_0]">
                            <span className="font-source text-sm font-normal leading-[150%]">
                              {_.get(inspectionSummary, 'feedback_correct_component', 0)}
                            </span>
                          </div>
                          <div className="flex flex-col justify-center items-end flex-[1_0_0] px-0 py-1 rounded-[2px_2px_0_0]">
                            <span className="font-source text-sm font-normal leading-[150%]">
                              {_.round(_.get(inspectionSummary, 'feedback_correct_component', 0) / (_.get(inspectionSummary, 'false_defect_count', 0) + _.get(inspectionSummary, 'feedback_correct_component', 0)) * 1000000, 0)}PPM
                            </span>
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center gap-6 self-stretch px-2 py-0">
                        <div className="flex items-start gap-0.5 flex-[1_0_0]">
                          <div className="flex flex-col justify-center items-start flex-[1_0_0] px-0 py-1 rounded-[2px_2px_0_0]">
                            <span className="font-source text-sm font-normal leading-[150%]">
                              {t('dataAnalysisReport.falsePositive')}
                            </span>
                          </div>
                          <div className="flex flex-col justify-center items-start flex-[1_0_0] px-0 py-1 rounded-[2px_2px_0_0]">
                            <span className="font-source text-sm font-normal leading-[150%]">
                              {_.get(inspectionSummary, 'false_defect_count', 0)}
                            </span>
                          </div>
                          <div className="flex flex-col justify-center items-end flex-[1_0_0] px-0 py-1 rounded-[2px_2px_0_0]">
                            <span className="font-source text-sm font-normal leading-[150%]">
                              {_.round(_.get(inspectionSummary, 'false_defect_count', 0) / (_.get(inspectionSummary, 'false_defect_count', 0) + _.get(inspectionSummary, 'feedback_correct_component', 0)) * 1000000, 0)}PPM
                            </span>
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center gap-6 self-stretch px-2 py-0">
                        <div className="flex items-start gap-0.5 flex-[1_0_0]">
                          <div className="flex flex-col justify-center items-start flex-[1_0_0] px-0 py-1 rounded-[2px_2px_0_0]">
                            <span className="font-source text-sm font-normal leading-[150%]">
                              {t('dataAnalysisReport.dpu')}
                            </span>
                          </div>
                          <div className="flex flex-col justify-center items-start flex-[1_0_0] px-0 py-1 rounded-[2px_2px_0_0]">
                            <span className="font-source text-sm font-normal leading-[150%]">
                              {_.round(_.get(inspectionSummary, 'defect_per_unit', 0), 2)}
                            </span>
                          </div>
                          <div className="flex flex-col justify-center items-end flex-[1_0_0] px-0 py-1 rounded-[2px_2px_0_0]">
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="flex items-center gap-6 self-stretch border-b-[color:var(--default-Gray-2,#4F4F4F)] [background:rgba(255,255,255,0.06)] px-2 py-0 border-b border-solid">
                  <div className="flex items-start gap-0.5 flex-[1_0_0]">
                    <div className="flex flex-col justify-center items-start opacity-[0.85] px-0 py-1 rounded-[2px_2px_0_0]">
                      <span className="font-source text-sm font-normal leading-[150%]">
                        {t('dataAnalysisReport.from')}
                      </span>
                    </div>
                    <div className="flex flex-col justify-center items-end flex-[1_0_0] px-0 py-1 rounded-[2px_2px_0_0]">
                      <span className="font-source text-sm font-normal leading-[150%]">
                        {startTime ? startTime.format('YYYY-MM-DD HH:mm:ss') : ''}
                      </span>
                    </div>
                  </div>
                  <div className="flex flex-col justify-center items-start opacity-[0.85] px-0 py-1 rounded-[2px_2px_0_0]">
                    -
                  </div>
                  <div className="flex justify-between items-center flex-[1_0_0] rounded-[2px_2px_0_0]">
                    <div className="flex flex-col justify-center items-start flex-[1_0_0] px-0 py-1 rounded-[2px_2px_0_0]">
                      <span className="font-source text-sm font-normal leading-[150%]">
                        {t('dataAnalysisReport.to')}
                      </span>
                    </div>
                    <div className="flex flex-col justify-center items-end flex-[1_0_0] px-0 py-1 rounded-[2px_2px_0_0]">
                      <span className="font-source text-sm font-normal leading-[150%]">
                        {endTime ? endTime.format('YYYY-MM-DD HH:mm:ss') : ''}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className="section-wrapper piechart-section flex flex-col self-stretch flex-1">
          <div className="flex flex-col justify-between items-center flex-[1_0_0] self-stretch [background:rgba(255,255,255,0.03)] px-0 py-4 rounded-sm">
            <div className="flex flex-col items-center gap-6 self-stretch">
              <div className="flex justify-center items-center gap-2 self-stretch">
                <span className="font-source text-base font-normal leading-[150%] capitalize">
                  {t('dataAnalysisReport.ngokRatio')}
                </span>
              </div>
              <div
                className={`printable-piechart ${activePrintSection === 'piechart' ? 'print-active-piechart' : ''} flex flex-col self-stretch cursor-pointer`}
                ref={pieChartContainer}
                onMouseEnter={() => handleSectionMouseEnter('piechart')}
                onMouseLeave={handleSectionMouseLeave}
              >
                <div className="flex h-[230px] flex-col justify-center items-center gap-2.5">
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie
                        data={boardNGPassRatioData}
                        cx="50%"
                        cy="50%"
                        innerRadius={50}
                        outerRadius={81}
                        paddingAngle={0}
                        dataKey="value"
                        startAngle={90}
                        endAngle={450}
                        label={({ name, percent }) => `${name} ${_.round(percent * 100, 2)}%`}
                      >
                        {boardNGPassRatioData.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={entry.color} />
                        ))}
                      </Pie>
                    </PieChart>
                  </ResponsiveContainer>
                </div>
                <div className="flex justify-center items-center gap-4 self-stretch">
                  <div className="flex items-center gap-1 px-0 py-1 rounded-[2px_2px_0_0]">
                    <div className="w-3 h-3 [background:linear-gradient(0deg,#42BE65_0%,#42BE65_100%),linear-gradient(0deg,rgba(0,0,0,0.10)_0%,rgba(0,0,0,0.10)_100%),var(--Passed,#42BE65)] rounded-sm" />
                    <span className="font-source text-sm font-normal leading-[150%] capitalize">
                      {t('common.pass')}
                    </span>
                  </div>
                  <div className="flex items-center gap-1 px-0 py-1 rounded-[2px_2px_0_0]">
                    <div className="w-3 h-3 [background:var(--semantic-colors-failed,#FA4D56)] rounded-sm" />
                    <span className="font-source text-sm font-normal leading-[150%] capitalize">
                      {t('common.ng')}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className="section-wrapper barchart-section flex flex-col self-stretch flex-1">
          {_.isInteger(selectedGoldenProdId) ?
            <div
              className={`printable-barchart ${activePrintSection === 'barchart' ? 'print-active-barchart' : ''} flex flex-col items-start gap-6 self-stretch [background:rgba(255,255,255,0.03)] px-3 py-4 flex-1 cursor-pointer`}
              ref={barChartContainer}
              onMouseEnter={() => handleSectionMouseEnter('barchart')}
              onMouseLeave={handleSectionMouseLeave}
            >
              <div className="flex h-8 justify-center items-center gap-2 self-stretch">
                <span className="font-source text-base font-normal leading-[150%] capitalize">
                  {t('dataAnalysisReport.topTenNGComponent')}
                </span>
              </div>
              <div className="flex flex-col flex-1 self-stretch">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart
                    data={topTenNGComponentData}
                    layout="vertical"
                    margin={{ top: 5, right: 30, left: 80, bottom: 5 }}
                  >
                    <CartesianGrid strokeDasharray="3 3" stroke="#4F4F4F" />
                    <XAxis
                      type="number"
                      stroke="#FFFFFF"
                      style={{ fontSize: '12px', fill: '#FFFFFF' }}
                    />
                    <YAxis
                      type="category"
                      dataKey={t('common.name')}
                      stroke="#FFFFFF"
                      tick={<CustomYAxisTick />}
                      interval={0}
                    />
                    <Tooltip
                      contentStyle={{
                        backgroundColor: '#333',
                        border: '1px solid #4F4F4F',
                        borderRadius: '4px',
                        color: '#FFFFFF'
                      }}
                    />
                    <Bar dataKey={t('common.amount')} fill="#56CCF2" radius={[0, 4, 4, 0]}>
                      {topTenNGComponentData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill="#56CCF2" />
                      ))}
                    </Bar>
                  </BarChart>
                </ResponsiveContainer>
              </div>
              <div className="flex justify-center items-center gap-4 self-stretch">
                <span className="font-source text-sm font-normal leading-[150%] capitalize">
                  {t('dataAnalysisReport.componentCount')}
                </span>
              </div>
            </div>
            :
            <div className="flex flex-1 flex-col justify-center items-center">
              <span className="font-source text-sm font-normal leading-[150%] capitalize">
                {t('dataAnalysisReport.pleaseSelectGoldenProduct')}
              </span>
            </div>
          }
        </div>
      </div>
      <div className='bottom-spacer flex flex-col justify-center items-center flex-[1_0_0] self-stretch p-4'>

      </div>
    </Fragment>
  );
};

export default Summary;