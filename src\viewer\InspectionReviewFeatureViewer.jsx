import React, { Fragment, useEffect, useRef, useState } from 'react';
import { fabric } from 'fabric';
import { newRectStrokeWidth, serverHost, isAOI2DSMT, leadFeatureType, reviewDetailMinFeatureComponentRatio } from '../common/const';
import { getAABBFromRect, getComponentCenterByRoiDtoObj, getFabricViewportCenter, getZoomNeededForObjs, rotatePoint, zoomPanToObject, zoomPanToObjects } from './util';
import _ from 'lodash';
import { Button, Switch, Tooltip } from 'antd';
import { useTranslation } from 'react-i18next';
import WindowedCroppedPointCloudDisplay from '../pages/teach/recipe/fullCaputre/WindowCropped3DDisplay';
import { useDispatch } from 'react-redux';
import { setCurrent3dSelectRectInfo } from '../reducer/display';
import { CustomSegmented } from '../common/styledComponent';
import { destroyFabricCanvas } from '../common/util';
import { ALERT_TYPES, aoiAlert } from '../common/alert';
import { mountingFeatureType, solderFeatureType, textFeatureType } from '../common/const';


const InspectionReviewFeatureViewer = (props) => {
  const {
    componentInfo,
    featureInfo,
    isErroredSample,
    isInspectedView,
    maskImage,
    isDepthMapDisplayed,
    shouldErrorLabelHidden,
    isLoadingRef,
  } = props;

  const dispatch = useDispatch();

  const { t } = useTranslation();

  const viewerContRef = useRef();
  const canvasElRef = useRef();
  const fcanvasRef = useRef();
  const scene = useRef();
  const selectedFeatureRectRef = useRef();
  const maskRefs = useRef([]);
  const curFeatureErrorTypeText = useRef();
  const curFeatureFeedbackDisplay = useRef();

  // const [isDepthMapDisplayed, setIsDepthMapDisplayed] = useState(false);
  const [threedViewerIdList, setThreedViewerIdList] = useState([]);
  const [featureComponentNotFound, setFeatureComponentNotFound] = useState(false);

  const updateZIndex = () => {
    if (scene.current) scene.current.moveTo(1);
    if (selectedFeatureRectRef.current) selectedFeatureRectRef.current.moveTo(2);
    maskRefs.current.forEach(m => m.moveTo(3));
    if (curFeatureErrorTypeText.current) curFeatureErrorTypeText.current.moveTo(maskRefs.current.length + 4);
    if (curFeatureFeedbackDisplay.current) curFeatureFeedbackDisplay.current.moveTo(maskRefs.current.length + 5);

    fcanvasRef.current.renderAll();
  };

  const init = async (componentInfo, featureInfo, isErroredSample, isInspectedView, isDepthMapDisplayed, maskImage) => {
    if (!fcanvasRef.current) return;

    if (isLoadingRef) isLoadingRef.current = true;

    // fetch image, load feature rect, and pan zoom to the selected feature
    let url = `${serverHost}/blob`;
    url += `?type=${isDepthMapDisplayed ? 'depth_map' : 'image'}`;
    url += `&color_uri=${encodeURIComponent(_.get(componentInfo, 'color_map_uri', ''))}`;
    url += `&depth_uri=${encodeURIComponent(_.get(componentInfo, 'depth_map_uri', ''))}`;
    url += `&t=${Date.now().valueOf()}`;
    url += `&sharpening_level=5`;
    
    const img = await new Promise((resolve, reject) => {
      fabric.util.loadImage(url, (img) => {
        resolve(img);
      }, null, 'anonymous');
    });

    // if (isInspectedView) {
    //   const download = async () => {
    //     const canvas = document.createElement('canvas');
    //     const ctx = canvas.getContext('2d');
    //     canvas.width = img.width;
    //     canvas.height = img.height;
    //     ctx.drawImage(img, 0, 0);
    //     const blob = await new Promise((resolve, reject) => {
    //       canvas.toBlob((blob) => {
    //         resolve(blob);
    //       }, 'image/jpeg');
    //     });
    //     const url = URL.createObjectURL(blob);
    //     const a = document.createElement('a');
    //     a.href = url;
    //     a.download = 'raw.jpg';
    //     a.click();
    //   };
    //   download();
    // }

    scene.current = new fabric.Image(img, {
      selectable: false,
      evented: false,
      top: 0,
      left: 0,
    });

    // scene.current.filters = scene.current.filters.filter((filter) => !(filter instanceof fabric.Image.filters.Convolute));
    // scene.current.filters.push(new fabric.Image.filters.Convolute({
    //   matrix: [ 0, -1, 0, -1, 5, -1, 0, -1, 0 ]
    // }));

    // if (isInspectedView) console.log('apply shappening filter start: ', new Date().getTime());
    // scene.current.applyFilters();
    // if (isInspectedView) console.log('apply shappening filter end: ', new Date().getTime());

    // download scene for testing purpose
    // if (isInspectedView) {
    //   const download = async () => {
    //     const canvas = document.createElement('canvas');
    //     const ctx = canvas.getContext('2d');
    //     canvas.width = scene.current.width;
    //     canvas.height = scene.current.height;
    //     ctx.drawImage(scene.current.getElement(), 0, 0);
    //     const blob = await new Promise((resolve, reject) => {
    //       canvas.toBlob((blob) => {
    //         resolve(blob);
    //       }, 'image/jpeg');
    //     });
    //     const url = URL.createObjectURL(blob);
    //     const a = document.createElement('a');
    //     a.href = url;
    //     a.download = 'shaprened.jpg';
    //     a.click();
    //   };
    //   download();
    // }

    scene.current.set('originalRectTopLeftWithZeroRotation', {
      top: 0,
      left: 0,
    });

    scene.current.set('originalRectInnerDim', {
      width: _.get(componentInfo, 'shape.points[1].x', 0) - _.get(componentInfo, 'shape.points[0].x', 0) + 1,
      height: _.get(componentInfo, 'shape.points[1].y', 0) - _.get(componentInfo, 'shape.points[0].y', 0) + 1,
    });

    if (!isInspectedView) {
      let featureCenter = getComponentCenterByRoiDtoObj(_.get(featureInfo, 'roi', {}));
      featureCenter = rotatePoint(featureCenter, -_.get(componentInfo, 'shape.angle', 0), getComponentCenterByRoiDtoObj(_.get(componentInfo, 'shape', {})));
      // featureCenter = rotatePoint(featureCenter, _.get(componentInfo, 'shape.angle', 0), getComponentCenterByRoiDtoObj(_.get(componentInfo, 'shape', {})));
      const fPMinPMax = {
        pMin: {
          x: featureCenter.x - (_.get(featureInfo, 'roi.points[1].x', 0) - _.get(featureInfo, 'roi.points[0].x', 0) + 1) / 2,
          y: featureCenter.y - (_.get(featureInfo, 'roi.points[1].y', 0) - _.get(featureInfo, 'roi.points[0].y', 0) + 1) / 2,
        },
        pMax: {
          x: featureCenter.x + (_.get(featureInfo, 'roi.points[1].x', 0) - _.get(featureInfo, 'roi.points[0].x', 0) + 1) / 2,
          y: featureCenter.y + (_.get(featureInfo, 'roi.points[1].y', 0) - _.get(featureInfo, 'roi.points[0].y', 0) + 1) / 2,
        },
      };
      
      selectedFeatureRectRef.current = new fabric.Rect({
        left: fPMinPMax.pMin.x - newRectStrokeWidth - _.get(componentInfo, 'shape.points[0].x', 0),
        top: fPMinPMax.pMin.y - newRectStrokeWidth - _.get(componentInfo, 'shape.points[0].y', 0),
        width: fPMinPMax.pMax.x - fPMinPMax.pMin.x + 1 + newRectStrokeWidth,
        height: fPMinPMax.pMax.y - fPMinPMax.pMin.y + 1 + newRectStrokeWidth,
        fill: 'transparent',
        stroke: isErroredSample ? 'red' : 'green',
        strokeWidth: newRectStrokeWidth,
        selectable: false,
        evented: false,
        strokeUniform: true,
      });
      selectedFeatureRectRef.current.rotate(_.get(featureInfo, 'roi.angle', 0) - _.get(componentInfo, 'shape.angle', 0));
    } else {
      selectedFeatureRectRef.current = new fabric.Rect({
        left: _.get(featureInfo, 'roi.points[0].x', 0) - newRectStrokeWidth,
        top: _.get(featureInfo, 'roi.points[0].y', 0) - newRectStrokeWidth,
        width: _.get(featureInfo, 'roi.points[1].x', 0) - _.get(featureInfo, 'roi.points[0].x', 0) + newRectStrokeWidth,
        height: _.get(featureInfo, 'roi.points[1].y', 0) - _.get(featureInfo, 'roi.points[0].y', 0) + newRectStrokeWidth,
        fill: 'transparent',
        stroke: isErroredSample ? 'red' : 'green',
        strokeWidth: newRectStrokeWidth,
        selectable: false,
        evented: false,
        strokeUniform: true,
      });

      selectedFeatureRectRef.current.rotate(_.get(featureInfo, 'roi.angle', 0));
    }

    selectedFeatureRectRef.current.set('originalRectTopLeftWithZeroRotation', {
      left: _.get(featureInfo, 'roi.points[0].x', 0) - newRectStrokeWidth,
      top: _.get(featureInfo, 'roi.points[0].y', 0) - newRectStrokeWidth,
    });

    selectedFeatureRectRef.current.set('originalRectInnerDim', {
      width: _.get(featureInfo, 'roi.points[1].x', 0) - _.get(featureInfo, 'roi.points[0].x', 0) + 1 + newRectStrokeWidth,
      height: _.get(featureInfo, 'roi.points[1].y', 0) - _.get(featureInfo, 'roi.points[0].y', 0) + 1 + newRectStrokeWidth,
    });
    
    fcanvasRef.current.add(scene.current);
    fcanvasRef.current.add(selectedFeatureRectRef.current);

    if (maskImage && isInspectedView) {
      const maskArray = Array.isArray(maskImage) ? maskImage : [maskImage];
      for (const m of maskArray) {
        let maskData = m;
        let roi = null;
        if (typeof m === 'object') {
          maskData = m.mask_image || m.image;
          roi = m.roi || null;
        }

        const maskImg = await new Promise((resolve) => {
          fabric.util.loadImage(maskData.startsWith('data') ? maskData : `data:image/jpeg;base64,${maskData}`, (img) => resolve(img));
        });
        const maskFabricImg = new fabric.Image(maskImg, {
          selectable: false,
          evented: false,
          opacity: 0.5,
          left: _.get(featureInfo, 'roi.points[0].x', 0),
          top: _.get(featureInfo, 'roi.points[0].y', 0),
        });

        if (roi) {
          let originalMaskCenter = maskFabricImg.getCenterPoint();
          originalMaskCenter.x += roi.x;
          originalMaskCenter.y += roi.y;
  
          const newMaskCenter = rotatePoint(originalMaskCenter, _.get(featureInfo, 'roi.angle', 0), getComponentCenterByRoiDtoObj(_.get(featureInfo, 'roi', {})));
  
          const translation = {
            x: newMaskCenter.x - originalMaskCenter.x + roi.x,
            y: newMaskCenter.y - originalMaskCenter.y + roi.y,
          };
  
          maskFabricImg.set({
            left: maskFabricImg.left + translation.x,
            top: maskFabricImg.top + translation.y,
          });
        }

        maskFabricImg.rotate(_.get(featureInfo, 'roi.angle', 0));
        fcanvasRef.current.add(maskFabricImg);
        maskRefs.current.push(maskFabricImg);
      }
    }

    // Add text display for text type features
    if (isInspectedView && !shouldErrorLabelHidden) {
      // get zoom needed for showing the feature rect
      const potentialZoom = getZoomNeededForObjs([selectedFeatureRectRef.current], fcanvasRef.current);

      // console.log('featureInfo', featureInfo);
      // console.log('componentInfo', componentInfo);

      // Try to extract text from the error structure first, then fallback to direct properties
      // let textToDisplay = t(`inferenceErrorType.${_.get(componentInfo, 'predicted_error_type', '')}`);
      // change to agent level error
      let textToDisplay = t(`inferenceErrorType.${_.get(featureInfo, 'error_type', '')}`);

      // Create background rectangle
      const textBgWidth = textToDisplay.length * 12 + 16;
      const textBgHeight = 24;

      const aabbBBox = getAABBFromRect({
        innerPMin: {
          x: _.get(featureInfo, 'roi.points[0].x', 0),
          y: _.get(featureInfo, 'roi.points[0].y', 0),
        },
        innerPMax: {
          x: _.get(featureInfo, 'roi.points[1].x', 0),
          y: _.get(featureInfo, 'roi.points[1].y', 0),
        },
        angle: _.get(featureInfo, 'roi.angle', 0),
        strokeWidth: newRectStrokeWidth,
      });

      curFeatureErrorTypeText.current = new fabric.Group([
        new fabric.Rect({
          left: 0,
          top: 0,
          width: textBgWidth,
          height: textBgHeight,
          fill: isErroredSample ? '#FF4444' : '#44AA44', // Red for fail, green for pass
        }),
        new fabric.Text(textToDisplay, {
          left: 8,
          top: 4,
          fontSize: 12,
          fill: 'white',
          fontFamily: 'Arial',
          fontWeight: 'bold',
        })
      ], {
        left: aabbBBox.pMin.x,
        top: aabbBBox.pMin.y - textBgHeight / potentialZoom,
        selectable: false,
        evented: false,
        scaleX: 1 / potentialZoom,
        scaleY: 1 / potentialZoom,
      });

      curFeatureErrorTypeText.current.setCoords();

      fcanvasRef.current.add(curFeatureErrorTypeText.current);

      if (!_.isEmpty(_.get(featureInfo, 'feedback'))) {
        const isNoErrorFeedback = (_.get(featureInfo, 'feedback.correct') === true && _.get(featureInfo, 'pass') === true) || (_.get(featureInfo, 'feedback.correct') === false && _.get(featureInfo, 'pass') === false);

        // Position feedback icon to the right of the feature rect

        const iconUrl = isNoErrorFeedback ? '/icn/thumbup_green.svg' : '/icn/thumbdown_red.svg';

        curFeatureFeedbackDisplay.current = await new Promise((resolve) => {
          fabric.util.loadImage(iconUrl, (img) => {
            resolve(new fabric.Image(img, {
              // left: selectedFeatureRectRef.current.strokeWidth + selectedFeatureRectRef.current.left + curFeatureErrorTypeText.current.width,
              // top: selectedFeatureRectRef.current.strokeWidth + selectedFeatureRectRef.current.top - iconSize - 4,
              left: aabbBBox.pMin.x + textBgWidth / potentialZoom + 2,
              top: aabbBBox.pMin.y - textBgHeight / potentialZoom + 1,
              // scaleX: iconSize / img.width,
              // scaleY: iconSize / img.height,
              scaleX: 1 / potentialZoom,
              scaleY: 1 / potentialZoom,
              selectable: false,
              evented: false,
            }));
          });
        });

        // curFeatureFeedbackDisplay.current.setCoords();

        fcanvasRef.current.add(curFeatureFeedbackDisplay.current);
      }
    }

    updateZIndex();

    if (selectedFeatureRectRef.current) {
      const featureRectAABBBBox = selectedFeatureRectRef.current.getBoundingRect(true);
      const featureRectAABBBBoxRect = new fabric.Rect({
        left: featureRectAABBBBox.left,
        top: featureRectAABBBBox.top,
        width: featureRectAABBBBox.width,
        height: featureRectAABBBBox.height,
      });
      featureRectAABBBBoxRect.set('originalRectTopLeftWithZeroRotation', {
        left: featureRectAABBBBox.left,
        top: featureRectAABBBBox.top,
      });
      featureRectAABBBBoxRect.set('originalRectInnerDim', {
        width: featureRectAABBBBox.width,
        height: featureRectAABBBBox.height,
      });

      // zoomPanToObject(selectedFeatureRectRef.current, fcanvasRef.current, 0.05);
      // zoomPanToObject(featureRectAABBBBoxRect, fcanvasRef.current, 0.05);
      const objs = [featureRectAABBBBoxRect];
      if (curFeatureErrorTypeText.current) objs.push(curFeatureErrorTypeText.current);
      if (curFeatureFeedbackDisplay.current) objs.push(curFeatureFeedbackDisplay.current);

      // For small ROI features (mount, solder, text), adjust viewport to prevent excessive zoom
      const featureType = _.get(featureInfo, 'feature_type', '');
      const isSmallRoiFeature = featureType === mountingFeatureType ||
                                 featureType === solderFeatureType ||
                                //  featureType === leadFeatureType ||
                                 _.startsWith(featureType, textFeatureType);

      if (isSmallRoiFeature) {
      // if (true) {
        // Get component dimensions
        const componentWidth = _.get(componentInfo, 'shape.points[1].x', 0) - _.get(componentInfo, 'shape.points[0].x', 0);
        const componentHeight = _.get(componentInfo, 'shape.points[1].y', 0) - _.get(componentInfo, 'shape.points[0].y', 0);
        const maxComponentDim = Math.max(componentWidth, componentHeight);

        // Calculate minimum viewport dimension (35% of max component dimension)
        const minViewportDim = reviewDetailMinFeatureComponentRatio * maxComponentDim;

        // Get current feature ROI dimensions
        const featureWidth = featureRectAABBBBox.width;
        const featureHeight = featureRectAABBBBox.height;
        const minFeatureDim = Math.max(featureWidth, featureHeight);

        // If feature is too small, expand the viewport rect
        // if (true) {
        if (minFeatureDim < minViewportDim) {
          const scaleFactor = minViewportDim / minFeatureDim;
          const newWidth = featureWidth * scaleFactor;
          const newHeight = featureHeight * scaleFactor;

          // Calculate center of feature rect
          const centerX = featureRectAABBBBox.left + featureWidth / 2;
          const centerY = featureRectAABBBBox.top + featureHeight / 2;

          // Create expanded rect centered on the feature
          const expandedRect = new fabric.Rect({
            left: centerX - newWidth / 2,
            top: centerY - newHeight / 2,
            width: newWidth,
            height: newHeight,
          });

          // Recalculate zoom for the expanded rect and update text/icon position
          // Keep using the original potentialZoom for text/icon scaling to maintain consistent size
          const actualZoom = getZoomNeededForObjs([expandedRect], fcanvasRef.current, 0.05);
          // const potentialZoom = selectedFeatureRectRef.current.get('potentialZoom');
          const absoultViewportLength = Math.min(fcanvasRef.current.getWidth(), fcanvasRef.current.getHeight());
          // make sure error text is not cut off 
          let potentialZoom = Math.min(actualZoom, absoultViewportLength / 200);

          if (curFeatureErrorTypeText.current && actualZoom && potentialZoom) {
            const textBgWidth = curFeatureErrorTypeText.current.width;
            // and text length not smaller then .4 of viewport width
            // potentialZoom = Math.min(potentialZoom, absoultViewportLength / (textBgWidth * 2.5));
            const textBgHeight = curFeatureErrorTypeText.current.height;
            const aabbBBox = getAABBFromRect({
              innerPMin: {
                x: _.get(featureInfo, 'roi.points[0].x', 0),
                y: _.get(featureInfo, 'roi.points[0].y', 0),
              },
              innerPMax: {
                x: _.get(featureInfo, 'roi.points[1].x', 0),
                y: _.get(featureInfo, 'roi.points[1].y', 0),
              },
              angle: _.get(featureInfo, 'roi.angle', 0),
              strokeWidth: newRectStrokeWidth,
            });

            // Use potentialZoom for scaling to keep text at consistent size
            // Use actualZoom for position calculation to place it correctly
            curFeatureErrorTypeText.current.set({
              left: aabbBBox.pMin.x,
              top: aabbBBox.pMin.y - textBgHeight / potentialZoom,
              scaleX: 1 / potentialZoom,
              scaleY: 1 / potentialZoom,
            });
            curFeatureErrorTypeText.current.setCoords();

            // Update feedback icon position if it exists
            if (curFeatureFeedbackDisplay.current) {
              curFeatureFeedbackDisplay.current.set({
                left: aabbBBox.pMin.x + textBgWidth / potentialZoom + 2,
                top: aabbBBox.pMin.y - textBgHeight / potentialZoom + 1,
                scaleX: 1 / potentialZoom,
                scaleY: 1 / potentialZoom,
              });
              curFeatureFeedbackDisplay.current.setCoords();
            }
          }

          // Use expanded rect for zoom calculation
          zoomPanToObjects([expandedRect, ...objs], fcanvasRef.current, 0.05);
          // keep zoom out extra 30%
          console.log('lol');
          const zoom = fcanvasRef.current.getZoom();
          const vpCenter = getFabricViewportCenter(fcanvasRef.current);
          fcanvasRef.current.zoomToPoint(vpCenter, zoom * 0.7);
          fcanvasRef.current.requestRenderAll();
        } else {
          zoomPanToObjects(objs, fcanvasRef.current, 0.05);
        }
      } else {
        zoomPanToObjects(objs, fcanvasRef.current, 0.05);
      }

      // zoomPanToObjects(objs, fcanvasRef.current, 0.05);
    }
    if (isLoadingRef) isLoadingRef.current = false;
  };

  const handleDisplayPointcloud = async (componentInfo, featureInfo, isInspectedView) => {
    let pMin;
    let pMax;
    let curImageUri;
    let curDepthImgUri;
    let angle;

    if (isInspectedView) {
      curImageUri = _.get(featureInfo, 'component_color_map_uri', '');
      curDepthImgUri = _.get(featureInfo, 'component_depth_map_uri', '');
      pMin = _.get(featureInfo, 'roi.points[0]', { x: 0, y: 0 });
      pMax = _.get(featureInfo, 'roi.points[1]', { x: 0, y: 0 });
      angle = _.get(featureInfo, 'roi.angle', 0);
    } else {
      curImageUri = _.get(componentInfo, 'color_map_uri', '');
      curDepthImgUri = _.get(componentInfo, 'depth_map_uri', '');
      pMin = {
        x: _.get(featureInfo, 'roi.points[0].x', 0) - _.get(componentInfo, 'shape.points[0].x', 0),
        y: _.get(featureInfo, 'roi.points[0].y', 0) - _.get(componentInfo, 'shape.points[0].y', 0),
      };
      pMax = {
        x: _.get(featureInfo, 'roi.points[1].x', 0) - _.get(componentInfo, 'shape.points[0].x', 0),
        y: _.get(featureInfo, 'roi.points[1].y', 0) - _.get(componentInfo, 'shape.points[0].y', 0),
      };
      angle = _.get(featureInfo, 'roi.angle', 0);
    }

		dispatch(
			setCurrent3dSelectRectInfo({
				pMin,
				pMax,
				curImageUri,
				curDepthImgUri,
        angle,
			})
		);

		const id = _.uniqueId();
		setThreedViewerIdList(prev => [...prev, id]);
	};

  useEffect(() => {
    if (!viewerContRef.current || !canvasElRef.current) return;

    if (!fcanvasRef.current) {
      fcanvasRef.current = new fabric.Canvas(canvasElRef.current, {
        antialias: 'off',
        uniformScaling: false,
        selection: false,
      });


      // enable webgl filter
      // let filterBackend = null;
      // try {
      //   filterBackend = new fabric.WebglFilterBackend();
      //   console.log('Use WebGL filter backend');
      // } catch (e) {
      //   console.error('WebGL backend is not supported, using 2d canvas backend');
      //   filterBackend = new fabric.Canvas2dFilterBackend();
      // }
      // fcanvasRef.current.filterBackend = filterBackend;
      // if (fabric.isWebglSupported()) {
      //   console.log('WebGL is supported, increase texture size to 65536');
      //   fcanvasRef.current.textureSize = 65536; // ow only partial image will be rendered if sharpness is enabled since our image is large
      // }
    }

    fcanvasRef.current.setWidth(viewerContRef.current.offsetWidth);
    fcanvasRef.current.setHeight(viewerContRef.current.offsetHeight);

    if (scene.current) {
      fcanvasRef.current.remove(scene.current);
      scene.current = null;
    }
    
    if (maskRefs.current.length > 0) {
      maskRefs.current.forEach(m => fcanvasRef.current.remove(m));
      maskRefs.current = [];
    }
    if (selectedFeatureRectRef.current) {
      fcanvasRef.current.remove(selectedFeatureRectRef.current);
      selectedFeatureRectRef.current = null;
    }

    // Clean up text and feedback displays
    if (curFeatureErrorTypeText.current) {
      fcanvasRef.current.remove(curFeatureErrorTypeText.current);
      curFeatureErrorTypeText.current = null;
    }
    if (curFeatureFeedbackDisplay.current) {
      fcanvasRef.current.remove(curFeatureFeedbackDisplay.current);
      curFeatureFeedbackDisplay.current = null;
    }

    if (!componentInfo || !featureInfo || featureInfo === undefined) {
      setFeatureComponentNotFound(true);
      if (isLoadingRef) isLoadingRef.current = false;
      return;
    } else {
      setFeatureComponentNotFound(false);
    }

    // if (isInspectedView) {
    //   console.log('=============================');
    //   console.log('componentInfo', componentInfo);
    //   console.log('featureInfo', featureInfo);
    //   console.log('isDepthMapDisplayed', isDepthMapDisplayed);
    //   console.log('maskImage', maskImage);
    // }

    if (isLoadingRef && isLoadingRef.current) {
      aoiAlert(t('notification.warning.pageIsStillLoading'), ALERT_TYPES.COMMON_WARNING);
      return;
    }

    init(componentInfo, featureInfo, isErroredSample, isInspectedView, isDepthMapDisplayed, maskImage);
  }, [featureInfo, isDepthMapDisplayed, maskImage]);

  useEffect(() => {
    return () => {
      if (fcanvasRef.current) {
        destroyFabricCanvas(fcanvasRef.current);
        fcanvasRef.current = null;
      }
    };
  }, []);

  return (
    <Fragment>
      {_.map(threedViewerIdList, (id) => (
        <WindowedCroppedPointCloudDisplay
          key={id}
          id={id}
          selfUnmount={(id) => {
            setThreedViewerIdList(_.filter(threedViewerIdList, (threedViewerId) => threedViewerId !== id));
          }}
        />  
      ))}
      <div className='relative w-full h-full'>
        {featureComponentNotFound &&
          <div className='absolute top-0 left-0 w-full h-full flex items-center justify-center bg-[#000] z-[30]'>
            <span className='font-source text-[14px] font-semibold leading-[150%]'>
              {t('review.featureComponentNotFound')}
            </span>
          </div>
        }
        {/* <div
          className='absolute right-0 z-[21]'
          style={{ top: 'calc(50% - 60px)' }}
        >
          <div className='flex gap-1 self-stretch p-1 items-center flex-col'>
            <CustomSegmented
              vertical
              value={null}
              onChange={(value) => {
                if (_.includes(['displayColorMap', 'displayDepthMap'], value)) setIsDepthMapDisplayed(value === 'displayColorMap' ? false : true);
                if (!isAOI2DSMT && value === 'displayPointcloud') handleDisplayPointcloud(componentInfo, featureInfo, isInspectedView);
              }}
              options={[
                {
                  value: isDepthMapDisplayed ? 'displayColorMap' : 'displayDepthMap',
                  label: <Tooltip
                    title={<span className='font-source text-[12px] font-semibold leading-[150%]'>
                      {isDepthMapDisplayed ? t('review.displayColorMap') : t('review.displayDepthMap')}
                    </span>}
                    placement='left'
                  >
                    <div className='flex w-8 h-8 justify-center items-center'>
                      <img
                        src={isDepthMapDisplayed ? '/icn/full_color.svg' : '/icn/depth_map.svg'}
                        alt='map'
                        className='w-6 h-6'
                      />
                    </div>
                  </Tooltip>,
                },
                ...(!isAOI2DSMT ? [{
                  value: 'displayPointcloud',
                  label: <Tooltip
                    title={<span className='font-source text-[12px] font-semibold leading-[150%]'>
                      {t('review.displayPointcloud')}
                    </span>}
                    placement='left'
                  >
                    <div className='flex w-8 h-8 justify-center items-center'>
                      <img
                        src='/icn/viewIn3D_white.svg'
                        alt='locator'
                        className='w-4 h-4'
                      />
                    </div>
                  </Tooltip>,
                }] : [])
              ]}
            />
          </div>
        </div> */}
        <div
          className='absolute top-0 left-0 w-full h-full z-[20]'
          ref={viewerContRef}
        >
          <canvas ref={canvasElRef} />
        </div>
      </div>
    </Fragment>
  );
};

export default InspectionReviewFeatureViewer;