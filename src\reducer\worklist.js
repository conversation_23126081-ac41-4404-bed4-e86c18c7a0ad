import { createSlice } from '@reduxjs/toolkit';


const initialState = {
  activeTab: 'summary',
  selectedGoldenProdId: null,
  startTime: null,
  endTime: null,
  snQuery: '',
  quickTimeQueryType: 'today',
  limit: 1000,
};

const worklist = createSlice({
  name: 'worklist',
  initialState,
  reducers: {
    setWorklistActiveTab: (state, action) => {
      state.activeTab = action.payload;
    },
    setWorklistSelectedGoldenProdId: (state, action) => {
      state.selectedGoldenProdId = action.payload;
    },
    setWorklistStartTime: (state, action) => {
      state.startTime = action.payload;
    },
    setWorklistEndTime: (state, action) => {
      state.endTime = action.payload;
    },
    setWorklistSnQuery: (state, action) => {
      state.snQuery = action.payload;
    },
    setWorklistQuickTimeQueryType: (state, action) => {
      state.quickTimeQueryType = action.payload;
    },
    setWorklistLimit: (state, action) => {
      state.limit = action.payload;
    },
    setWorklistState: (state, action) => {
      // Set all worklist state at once
      return { ...state, ...action.payload };
    },
    resetWorklistState: () => {
      return initialState;
    },
  },
});

export const {
  setWorklistActiveTab,
  setWorklistSelectedGoldenProdId,
  setWorklistStartTime,
  setWorklistEndTime,
  setWorklistSnQuery,
  setWorklistQuickTimeQueryType,
  setWorklistLimit,
  setWorklistState,
  resetWorklistState,
} = worklist.actions;

export default worklist.reducer;

