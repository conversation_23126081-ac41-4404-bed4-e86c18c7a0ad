import { Button, Tabs } from 'antd';
import React, { Fragment, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import MarkAlignPCB from './markAlignPCB/MarkAlignPCB';
import TemplateEditor from './templateEditor/TemplateEditor';
import PCBArray from './PCBArray/Index';
import { useGetAllFeaturesQuery, useGetArrayRegisterationQuery } from '../../../services/product';
import _ from 'lodash';
import { useLazyGetAllConveyorStatusQuery } from '../../../services/conveyor';
import { getRunningTaskGoldenProductIds } from '../../../common/util';
import { useDispatch } from 'react-redux';
import { setContainerLvlLoadingMsg, setIsContainerLvlLoadingEnabled } from '../../../reducer/setting';
import { ALERT_TYPES, aoiAlert } from '../../../common/alert';
import { useLazyGetInferenceStatusQuery } from '../../../services/inference';


const Components = (props) => {
  const {
    curProduct,
    productId,
		refetchCurProduct,
    isFromUpdateCad,
  } = props;

  const dispatch = useDispatch();

  const { t } = useTranslation();
  const init = useRef(true);
  const { data: markerFeatures, refetch: refetchMarkers } = useGetAllFeaturesQuery({ product_id: productId, marker: true, step: '0' });
  const {
    data: arrayRegisteration,
    refetch: refetchArrayRegisteration,
    isFetching: isFetchingArrayRegisteration,
    isUninitialized: isUninitializedArrayRegisteration,
    isLoading: isLoadingArrayRegisteration,
  } = useGetArrayRegisterationQuery({ product_id: Number(productId), step: 0 });
  const [lazyGetInferenceStatus] = useLazyGetInferenceStatusQuery();

  const [activeTab, setActiveTab] = useState('markAlignPCB');
  const [isRedefiningInspectionRegion, setIsRedefiningInspectionRegion] = useState(false);
  const [isDrawModeEnabled, setIsDrawModeEnabled] = useState(false);

  const handleTabChange = useCallback((key) => {
    setActiveTab(key);
    if (key === 'markAlignPCB') refetchMarkers();
  }, []);

  const handleRedefineInspectionRegion = useCallback(async (productId) => {
    const runGoldenIds = await getRunningTaskGoldenProductIds(lazyGetInferenceStatus);

    if (_.includes(runGoldenIds, Number(productId))) {
      dispatch(setIsContainerLvlLoadingEnabled(false));
      dispatch(setContainerLvlLoadingMsg(''));
      aoiAlert(t('notification.error.pleaseStopAllInspectionTaskRelatedToThisProduct'), ALERT_TYPES.COMMON_ERROR);
      return;
    }

    setIsRedefiningInspectionRegion(!isRedefiningInspectionRegion);
  }, [isRedefiningInspectionRegion]);

  useEffect(() => {
    if (isFromUpdateCad) {
      setActiveTab('templateEditor');
    }

    if(!init.current) {
      init.current = true;
    }

    refetchArrayRegisteration();
  }, []);


  useEffect(() => {
    if (!markerFeatures || !init.current) {
      return;
    } else if (markerFeatures && markerFeatures.length > 0) {
      setActiveTab('templateEditor');
    } else if (markerFeatures && markerFeatures.length === 0) {
      setActiveTab('markAlignPCB');
    } else {
      setActiveTab('markAlignPCB');
    }

    init.current = false;
  }, [markerFeatures, init]);

  const tabsItems = useMemo(() => [
    {
      label: <div className='flex py-2 gap-2 items-center justify-center'>
        <span className={`font-source text-[14px] font-${activeTab === 'markAlignPCB' ? 'semibold' : 'normal'} leading-[normal]`}>
          {t('productDefine.markAlignPCB')}
        </span>
      </div>,
      key: 'markAlignPCB',
      disabled: isRedefiningInspectionRegion,
    },
    {
      label: <div className='flex py-2 gap-2 items-center justify-center'>
        <span className={`font-source text-[14px] font-${activeTab === 'templateEditor' ? 'semibold' : 'normal'} leading-[normal]`}>
          {t('productDefine.templateEditor')}
        </span>
      </div>,
      key: 'templateEditor',
      disabled: isRedefiningInspectionRegion,
    },
    {
      label: <div className='flex py-2 gap-2 items-center justify-center'>
        <span className={`font-source text-[14px] font-${activeTab === 'PCBArray' ? 'semibold' : 'normal'} leading-[normal]`}>
          {t('productDefine.pcbaArray')}
        </span>
      </div>,
      key: 'PCBArray',
    },
  ], [activeTab, isRedefiningInspectionRegion, t]);

  const buttonContent = useMemo(() => {
    if (isRedefiningInspectionRegion) {
      return (
        <span className='text-red font-source text-xs font-normal leading-[150%] pt-[3px]'>
          {t('common.quit')}
        </span>
      );
    }

    return (
      <div className='flex justify-center items-center gap-1 rounded px-2 py-1.5'>
        <div className='flex w-4 h-4 flex-col justify-center items-center gap-2.5 rounded'>
          <img
            src='/icn/grid_blue.svg'
            className='w-2 h-2 shrink-0 stroke-[1px] stroke-[color:var(--AOI-blue,#56CCF2)]'
          />
        </div>
        <span className='text-AOI-blue font-source text-xs font-normal leading-[150%] pt-[3px]'>
          {t('productDefine.redefineInpsectionRegion')}
        </span>
      </div>
    );
  }, [isRedefiningInspectionRegion, t]);

  if (
    isUninitializedArrayRegisteration ||
    isLoadingArrayRegisteration ||
    isFetchingArrayRegisteration
  ) return null;

  return (
    <Fragment>
      <div className='flex items-center self-stretch h-[62px] border-b-[#4F4F4F] border-b-[1px]'>
        <Tabs
          // style={{ width: '100%' }}
          activeKey={activeTab}
          onChange={handleTabChange}
          type='card'
          items={tabsItems}
        />
        {activeTab === 'templateEditor' && !isDrawModeEnabled &&
          <Button
            type='text'
            onClick={() => handleRedefineInspectionRegion(productId)}
          >
            {buttonContent}
          </Button>
        }
      </div>
      <div className='flex flex-1 px-0.2 self-stretch rounded-[6px]'>
        {activeTab === 'markAlignPCB' && (
          <MarkAlignPCB
            curProduct={curProduct}
            markerFeatures={markerFeatures}
            refetchMarkers={refetchMarkers}
          />
        )}
        {activeTab === 'templateEditor' && (
          <TemplateEditor
            curProduct={curProduct}
            productId={productId}
						refetchCurProduct={refetchCurProduct}
            isRedefiningInspectionRegion={isRedefiningInspectionRegion}
            setIsRedefiningInspectionRegion={setIsRedefiningInspectionRegion}
            isDrawModeEnabled={isDrawModeEnabled}
            setIsDrawModeEnabled={setIsDrawModeEnabled}
            arrayRegisteration={arrayRegisteration} // for get current viewport array index
            refetchArrayRegisteration={refetchArrayRegisteration}
          />
        )}
        {activeTab === 'PCBArray' && (
          <PCBArray
            curProduct={curProduct}
            productId={productId}
            refetchCurProduct={refetchCurProduct}
            setComponentsActiveTab={setActiveTab}
            arrayRegisteration={arrayRegisteration}
            refetchArrayRegisteration={refetchArrayRegisteration}
          />
        )}
      </div>
    </Fragment>
  );
};

export default Components;