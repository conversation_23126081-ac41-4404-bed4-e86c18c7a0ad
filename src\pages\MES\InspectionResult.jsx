import { useEffect, useState, useRef } from "react";
import { useTranslation } from "react-i18next";
import { useDeleteInspectionRecordByProductIdMutation, useGetAllInspectionsQuery } from "../../services/inference";
import { backendAutoGenTimeToDisplayString, toLocalISOString } from "../../common/util";
import _ from 'lodash';
import { Button, Tooltip } from "antd";
import { useNavigate } from "react-router-dom";
import { worklistMaxDisplayedCount } from "../../common/const";
import { useDispatch } from "react-redux";
import { setContainerLvlLoadingMsg, setIsContainerLvlLoadingEnabled } from "../../reducer/setting";


const InspectionResult = (props) => {
  const {
    selectedGoldenProdId,
    startTime,
    endTime,
    snQuery,
    limit,
  } = props;

  // if (_.isNull(startTime) || _.isNull(endTime)) return null;

  const dispatch = useDispatch();

  const navigate = useNavigate();

  const { t } = useTranslation();
  const tableRef = useRef(null);
  const [tableHeight, setTableHeight] = useState(0);

  const [deleteInspectionRecord] = useDeleteInspectionRecordByProductIdMutation();

  useEffect(() => {
    if (!tableRef.current) return;

    const handleResize = () => {
      if (!tableRef.current) return;
      // Calculate available height: subtract header (56px) from parent height
      const availableHeight = tableRef.current.parentElement.offsetHeight - 56;
      setTableHeight(availableHeight);
    };

    const resizeObserver = new ResizeObserver(handleResize);
    resizeObserver.observe(tableRef.current.parentElement);
    handleResize();

    return () => {
      resizeObserver.disconnect();
    };
  }, []);

  const {
    data: inpsections,
    isFetching: inpsectionsFetching,
    isLoading: inpsectionsLoading,
    refetch: refetchInspections,
  } = useGetAllInspectionsQuery({
    serial_no: _.isEmpty(snQuery) ? undefined : snQuery,
    start_datetime: _.isNull(startTime) ? undefined : toLocalISOString(new Date(startTime)),
    end_datetime: _.isNull(endTime) ? undefined : toLocalISOString(new Date(endTime)),
    page: 0,
    limit, // Fetch all data
    golden_product_id: _.isInteger(selectedGoldenProdId) ? Number(selectedGoldenProdId) : undefined,
  }, {
    refetchOnMountOrArgChange: true,
  });

  const inspectionData = _.get(inpsections, 'data', []);

  const handleExportCSV = () => {
    if (_.isEmpty(inspectionData)) {
      return;
    }

    // Define CSV headers
    const headers = [
      t('dataAnalysisReport.pcbaName'),
      t('dataAnalysisReport.sn'),
      t('dataAnalysisReport.totalComponent'),
      t('dataAnalysisReport.ngComponentCount'),
      t('dataAnalysisReport.alertedComponentCount'),
      t('dataAnalysisReport.inspectionStartTime'),
      t('dataAnalysisReport.inspectionEndTime'),
    ];

    // Create CSV rows
    const rows = inspectionData.map(record => [
      record.golden_product_name || '',
      record.product_serial_no || '',
      record.total_component_count || 0,
      record.feedback_ng_component_count || 0,
      record.defective_component_count || 0,
      record.time_started ? backendAutoGenTimeToDisplayString(record.time_started) : '',
      record.time_finished ? backendAutoGenTimeToDisplayString(record.time_finished) : '',
    ]);

    // Combine headers and rows
    const csvContent = [
      headers.join(','),
      ...rows.map(row => row.map(cell => `"${cell}"`).join(','))
    ].join('\n');

    // Create blob and download
    const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `inspection_results_${new Date().getTime()}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const cols = [
    {
      key: 'pcbaName',
      title: <span className="font-source text-[12px] font-normal leading-[150%] text-gray-5">{t('dataAnalysisReport.pcbaName')}</span>,
      render: (text, record) => (
        <span className="font-source text-[12px] font-normal leading-[150%]">
          {record.golden_product_name}
        </span>
      ),
    },
    {
      key: 'sn',
      title: <span className="font-source text-[12px] font-normal leading-[150%] text-gray-5">{t('dataAnalysisReport.sn')}</span>,
      render: (text, record) => (
        <span className="font-source text-[12px] font-normal leading-[150%]">
          {record.product_serial_no}
        </span>
      ),
    },
    {
      key: 'totalComponent',
      title: <span className="font-source text-[12px] font-normal leading-[150%] text-gray-5">{t('dataAnalysisReport.totalComponent')}</span>,
      render: (text, record) => (
        <span className="font-source text-[12px] font-normal leading-[150%]">
          {record.total_component_count}
        </span>
      ),
    },
    {
      key: 'ngComponentCount',
      title: <span className="font-source text-[12px] font-normal leading-[150%] text-gray-5">{t('dataAnalysisReport.ngComponentCount')}</span>,
      render: (text, record) => (
        <span className="font-source text-[12px] font-normal leading-[150%]">
          {record.feedback_ng_component_count}
        </span>
      ),
    },
    {
      key: 'alertedComponentCount',
      title: <span className="font-source text-[12px] font-normal leading-[150%] text-gray-5">{t('dataAnalysisReport.alertedComponentCount')}</span>,
      render: (text, record) => (
        <span className="font-source text-[12px] font-normal leading-[150%]">
          {record.defective_component_count}
        </span>
      ),
    },
    {
      key: 'inspectionStartTime',
      title: <span className="font-source text-[12px] font-normal leading-[150%] text-gray-5">{t('dataAnalysisReport.inspectionStartTime')}</span>,
      render: (text, record) => (
        <span className="font-source text-[12px] font-normal leading-[150%]">
          {_.get(record, 'time_started') ? backendAutoGenTimeToDisplayString(record.time_started) : ''}
        </span>
      ),
    },
    {
      key: 'inspectionEndTime',
      title: <span className="font-source text-[12px] font-normal leading-[150%] text-gray-5">{t('dataAnalysisReport.inspectionEndTime')}</span>,
      render: (text, record) => (
        <span className="font-source text-[12px] font-normal leading-[150%]">
          {_.get(record, 'time_finished') ? backendAutoGenTimeToDisplayString(record.time_finished) : ''}
        </span>
      ),
    },
    {
      key: 'actions',
      title: <span className="font-source text-[12px] font-normal leading-[150%] text-gray-5">{t('common.actions')}</span>,
      render: (text, record) => (
        <div className="flex items-center gap-2">
          <Button
            type='text'
            size="small"
            onClick={() => {
              navigate(`/inspection/review?ipc-product-id=${record.product_id}&ipc-session-id=${record.ipc_session_id}&golden-product-id=${record.golden_product_id}`);
            }}
          >
            <span className="font-source text-[12px] font-normal leading-[150%] text-AOI-blue">
              {t('common.review')}
            </span>
          </Button>
          <Button
            type='text'
            size="small"
            onClick={() => {
              let url = `/inspection-detail-export?`;
              url += `ipc-product-id=${record.product_id}`;
              url += `&ipc-session-id=${record.ipc_session_id}`;
              url += `&golden-product-id=${record.golden_product_id}`;
              url += `&ipc-product-name=${record.golden_product_name}`;
              url += `&inspection-time=${record.time_started}`;
              window.open(url, '_blank');
            }}
          >
            <span className="font-source text-[12px] font-normal leading-[150%] text-AOI-blue">
              {t('dataAnalysisReport.exportReport')}
            </span>
          </Button>
          <Button
            type='text'
            size="small"
            onClick={() => {
              const run = async (productId) => {
                dispatch(setIsContainerLvlLoadingEnabled(true));
                dispatch(setContainerLvlLoadingMsg(t('loader.removeInferenceRecord')));
  
                const res = await deleteInspectionRecord(productId);
  
                dispatch(setIsContainerLvlLoadingEnabled(false));
                dispatch(setContainerLvlLoadingMsg(''));
  
                if (res.error) {
                  aoiAlert(t('notification.error.deleteInspectionRecord'), ALERT_TYPES.COMMON_ERROR);
                  console.error('deleteInspectionRecord error: ', res.error.message);
                  return;
                }
  
                await refetchInspections();
              };

              run(record.product_id);
            }}
          >
            <span className="font-source text-[12px] font-normal leading-[150%] text-AOI-blue">
              {t('dataAnalysisReport.deleteRecord')}
            </span>
          </Button>
        </div>
      ),
    }
  ];

  return (
    <>
      <style>{`
        @media print {
          body * {
            visibility: hidden;
          }
          .printable-section, .printable-section * {
            visibility: visible;
          }
          .printable-section {
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
            height: auto !important;
            overflow: visible !important;
          }
          .printable-section .no-print {
            display: none !important;
          }
          .printable-section > div {
            height: auto !important;
            overflow: visible !important;
          }
          .printable-section > div > div:last-child {
            height: auto !important;
            overflow: visible !important;
          }
        }
      `}</style>
      <div className="flex flex-col items-center flex-[1_0_0] self-stretch px-4 py-0">
        <div className="flex items-center justify-between self-stretch px-0 py-2 h-[56px] gap-2">
          <span className="font-source text-base font-semibold leading-6">
            {t('dataAnalysisReport.inspectionResult')} | {t('common.total')}: {inspectionData.length}
          </span>
          <Button
            onClick={handleExportCSV}
            disabled={_.isEmpty(inspectionData)}
            className="flex items-center gap-1"
          >
            <img src="/icn/download_white.svg" alt="export" className="w-4 h-4" />
            <span className="font-source text-[12px] font-normal leading-[150%]">
              {t('common.export')} CSV
            </span>
          </Button>
          {/* <Tooltip
            title={<span className="font-source text-[12px] font-normal leading-[150%]">{t('dataAnalysisReport.maxDisplayedCount')}</span>}
          >
            <img
              src='/icn/info_white.svg'
              alt='info'
              className='w-[16px] h-[16px] ml-2'
            />
          </Tooltip> */}
        </div>
        <div className="printable-section flex flex-col items-start self-stretch flex-1 cursor-pointer" ref={tableRef}>
          <div className="flex flex-col items-start self-stretch rounded border border-[#333] border-solid" style={{ height: `${tableHeight}px` }}>
            {/* Table Header */}
            <div className="flex items-center gap-4 self-stretch border-b-[#333] [background:rgba(0,0,0,0.03)] px-2 py-0 border-b border-solid sticky top-0 z-10">
              {cols.map((col, idx) => (
                <div key={col.key} className={`flex items-center ${idx === cols.length - 1 ? 'w-[200px]' : 'flex-1'}`}>
                  {col.title}
                </div>
              ))}
            </div>
            {/* Table Body - Scrollable */}
            <div className="flex flex-col items-start self-stretch overflow-auto flex-1">
              {inpsectionsLoading || inpsectionsFetching ? (
                <div className="flex justify-center items-center self-stretch flex-1">
                  <span className="font-source text-sm font-normal leading-[150%]">{t('common.loading')}...</span>
                </div>
              ) : (
                inspectionData.map((record, idx) => (
                  <div key={idx} className="flex items-center gap-4 self-stretch border-b-[color:var(--default-Gray-6,#F2F2F2)] px-2 py-2 border-b border-solid">
                    {cols.map((col, colIdx) => (
                      <div key={col.key} className={`flex items-center ${colIdx === cols.length - 1 ? 'w-[200px] no-print' : 'flex-1'}`}>
                        {col.render ? col.render(null, record) : record[col.key]}
                      </div>
                    ))}
                  </div>
                ))
              )}
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default InspectionResult;