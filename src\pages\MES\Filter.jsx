import { Button, DatePicker, Input, InputNumber, Select, Tooltip } from "antd";
import React, { useEffect, useState } from "react";
import _ from "lodash";
import { useTranslation } from "react-i18next";
import i18n from "../../i18n";
import { customZhCNDatePickerLocale } from '../../common/const';
import enUS from 'antd/es/date-picker/locale/en_US';
import { SearchOutlined } from "@ant-design/icons";


const { RangePicker } = DatePicker;

const Filter = (props) => {
  const {
    selectedGoldenProdId,
    setSelectedGoldenProdId,
    allProducts,
    startTime,
    setStartTime,
    endTime,
    setEndTime,
    snQuery,
    setSnQuery,
    quickTimeQueryType,
    setQuickTimeQueryType,
    limit,
    setLimit,
    activeTab,
  } = props;

  const { t } = useTranslation();

  const [displayedLimit, setDisplayedLimit] = useState(limit);

  useEffect(() => {
    setDisplayedLimit(limit);
  }, [limit]);

  return (
    <div className='flex justify-between items-center self-stretch border-b-[color:var(--default-Gray-1,#333)] px-3 py-2 border-b border-solid'>
      <div className="flex items-center gap-2">
        <Select
          showSearch
          options={
            _.isEmpty(allProducts) ? []
            : _.filter(allProducts, (product) => product.is_golden === true).map((product) => ({
              value: Number(product.product_id),
              label: <span className='font-source text-[12px] font-normal'>{product.product_name}</span>,
              searchKey: product.product_name,
            }))
          }
          value={selectedGoldenProdId}
          onChange={(value) => setSelectedGoldenProdId(value)}
          placeholder={<span className='font-source text-[12px] font-normal'>{t('worklist.filterByGoldenProduct')}</span>}
          style={{ width: '200px' }}
          popupMatchSelectWidth={false}
          allowClear
          filterOption={(input, option) => {
            return option.searchKey.toLowerCase().indexOf(input.toLowerCase()) >= 0;
          }}
        />
        <div className="flex h-[42px] items-center [background:rgba(0,0,0,0.10)] rounded-md border-solid">
          <RangePicker
            locale={i18n.language === 'cn' ? customZhCNDatePickerLocale : enUS}
            showTime
            onCalendarChange={(value) => {
              setStartTime(_.get(value, '0', null));
              setEndTime(_.get(value, '1', null));
            }}
            value={[startTime, endTime]}
          />
        </div>
        <Button
          style={{ height: '32px' }}
          onClick={() => setQuickTimeQueryType('today')}
          type={quickTimeQueryType === 'today' ? 'primary' : 'default'}
        >
          <span className="font-source text-[12px] font-normal">
            {t('dataAnalysisReport.today')}
          </span>
        </Button>
        <Button
          style={{ height: '32px' }}
          onClick={() => setQuickTimeQueryType('yesterday')}
          type={quickTimeQueryType === 'yesterday' ? 'primary' : 'default'}
        >
          <span className="font-source text-[12px] font-normal">
            {t('dataAnalysisReport.yesterday')}
          </span>
        </Button>
        <Button
          style={{ height: '32px' }}
          onClick={() => setQuickTimeQueryType('thisWeek')}
          type={quickTimeQueryType === 'thisWeek' ? 'primary' : 'default'}
        >
          <span className="font-source text-[12px] font-normal">
            {t('dataAnalysisReport.thisWeek')}
          </span>
        </Button>
        <Button
          style={{ height: '32px' }}
          onClick={() => setQuickTimeQueryType('thisMonth')}
          type={quickTimeQueryType === 'thisMonth' ? 'primary' : 'default'}
        >
          <span className="font-source text-[12px] font-normal">
            {t('dataAnalysisReport.thisMonth')}
          </span>
        </Button>
        <Button
          style={{ height: '32px' }}
          onClick={() => setQuickTimeQueryType('thisYear')}
          type={quickTimeQueryType === 'thisYear' ? 'primary' : 'default'}
        >
          <span className="font-source text-[12px] font-normal">
            {t('dataAnalysisReport.thisYear')}
          </span>
        </Button>
        <Input
          style={{ height: '32px', width: '200px' }}
          addonBefore={<SearchOutlined />}
          value={snQuery}
          onChange={(e) => setSnQuery(e.target.value)}
          allowClear
          placeholder={t('dataAnalysisReport.searchBySerialNo')}
        />
        <Tooltip
          title={
            <span className='font-source text-[12px] font-normal'>
              {t('worklist.clearFilter')}
            </span>
          }
        >
          <Button
            style={{ height: '32px' }}
            onClick={() => {
              setSelectedGoldenProdId(null);
              setStartTime(null);
              setEndTime(null);
              setSnQuery('');
              setQuickTimeQueryType(null);
            }}
          >
            <img className='w-[16px] h-[16px]' src='/icn/cancelFilter_blue.svg' alt='cancelFilter' />
          </Button>
        </Tooltip>
        {activeTab !== 'summary' &&
          <div className="flex items-center gap-2">
            <span className="font-source text-[12px] font-normal">
              {t('dataAnalysisReport.maxDisplayedCount')}
            </span>
            <InputNumber
              style={{ width: '80px' }}
              // controls={false}
              min={1}
              value={displayedLimit}
              onChange={(value) => setDisplayedLimit(value)}
              precision={0}
              step={1}
              onPressEnter={() => setLimit(displayedLimit)}
            />
            <Button
              onClick={() => setLimit(displayedLimit)}
            >
              <span className="font-source text-[12px] font-normal">
                {t('common.apply')}
              </span>
            </Button>
          </div>
        }
      </div>
      {/* <div className="flex h-[42px] justify-center items-center gap-2 rounded border border-[color:var(--default-Gray-1,#333)] px-3 py-2 border-solid">
        
      </div> */}
      {/* <Button
        style={{ height: '32px' }}
      >
        <div className="flex gap-2 items-center">
          <img src='/icn/download_blue.svg' alt='download' className='w-[17px] h-[16px]' />
          <span className="font-source text-[12px] font-normal">
            {t('common.exportAll')}
          </span>
        </div>
      </Button> */}
    </div>
  );
};

export default Filter;