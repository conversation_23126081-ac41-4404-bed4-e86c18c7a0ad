{"name": "aoi_pcb_web_client", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "cross-env GENERATE_SOURCEMAP=false vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@esbuild-plugins/node-globals-polyfill": "^0.2.3", "@esbuild-plugins/node-modules-polyfill": "^0.2.2", "@jaames/iro": "^5.5.2", "@reduxjs/toolkit": "^2.3.0", "antd": "^5.23.3", "csv-parse": "^5.6.0", "dayjs": "^1.11.18", "fabric": "^5.5.0", "html2canvas": "^1.4.1", "i18next": "^23.16.5", "i18next-browser-languagedetector": "^8.0.0", "i18next-sprintf-postprocessor": "^0.2.2", "jsoneditor": "^10.1.1", "jsonwebtoken": "^9.0.2", "jspdf": "^3.0.3", "jspdf-autotable": "^5.0.2", "md5": "^2.3.0", "moment": "^2.30.1", "react": "^18.3.1", "react-dom": "^18.3.1", "react-i18next": "^15.1.1", "react-redux": "^9.1.2", "react-rnd": "^10.4.14", "react-router-dom": "^6.28.0", "react-toastify": "^11.0.3", "recharts": "^2.8.0", "redux": "^5.0.1", "redux-persist": "^6.0.0", "redux-persist-transform-filter": "^0.0.22", "redux-thunk": "^3.1.0", "styled-components": "^6.1.14", "three": "^0.167.0"}, "devDependencies": {"@eslint/js": "^9.17.0", "@types/react": "^18.3.18", "@types/react-dom": "^18.3.5", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.20", "buffer": "^6.0.3", "cross-env": "^7.0.3", "eslint": "^9.17.0", "eslint-plugin-react": "^7.37.2", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.16", "globals": "^15.14.0", "postcss": "^8.4.49", "rollup-plugin-node-polyfills": "^0.2.1", "tailwindcss": "^3.4.6", "vite": "^6.0.5"}}