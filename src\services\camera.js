import { createApi } from '@reduxjs/toolkit/query/react';
import { baseQuery } from './api';
import _ from 'lodash';


export const cameraApi = createApi({
  reducerPath: 'cameraApi',
  baseQuery,
  tagTypes: ['Camera'],
  endpoints: (build) => ({
    moveCamera: build.mutation({
      query: (body) => ({
        url: '/moveCamera',
        method: 'POST',
        body,
      }),
    }),
    acquireCameraControl: build.mutation({
      query: (body) => ({
        url: '/cameraControl',
        method: 'POST',
        body,
      }),
    }),
    releaseCameraControl: build.mutation({
      query: (body) => ({
        url: '/releaseCameraControl',
        method: 'POST',
        body,
      }),
    }),
    getCameraCaptureFrameUri: build.query({
      query: (params) => {
        return {
          url: '/camera/preview',
          method: 'GET',
          params,
        };
      },
    }),
    getImageMetaData: build.query({
      query: (params) => {
        if (_.isEmpty(_.get(params, 'uri', null))) throw new Error('uri emtpy');
        return {
          url: '/image/metadata',
          method: 'GET',
          params,
        };
      },
    }),
    inFieldCalibration: build.mutation({
      query: () => ({
        url: '/camera/calibration',
        method: 'POST',
      }),
    }),
    cameraMotionCalibration: build.mutation({
      query: (body) => ({
        url: '/camera/motionCalibration',
        method: 'POST',
        body,
      }),
    }),
    getCameraSonsorConfig: build.query({
      query: () => ({
        url: '/camera/configs',
        method: 'GET',
      }),
    }),
  }),
});

export const {
  useMoveCameraMutation,
  useAcquireCameraControlMutation,
  useReleaseCameraControlMutation,
  useLazyGetCameraCaptureFrameUriQuery,
  useGetImageMetaDataQuery,
  useLazyGetImageMetaDataQuery,
  useInFieldCalibrationMutation,
  useCameraMotionCalibrationMutation,
  useLazyGetCameraSonsorConfigQuery,
} = cameraApi;