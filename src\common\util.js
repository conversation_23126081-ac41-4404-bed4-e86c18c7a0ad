import moment from 'moment';
import { parse } from 'csv-parse';
import _ from 'lodash';
import md5 from 'md5';
import { agentParamStatsList, barcodeScanner, doubleSidedSliderAgentParams, lead2DColorAgentParamNames, leadFeatureType, leadGapFeatureType, leadInspection2D, leadInspection2DBase, leadInspection3D, mounting3DExtendedRoiAgentParamNames, mountingFeatureType, mountingInspection2D, mountingInspection3D, polarityCheckThreshold, polarityRoi, profileRoiAgentParamNames, retrainModelTaskPhaseType, solder2DColorAgentParamNames, solderFeatureType, solderInspection2D, solderInspection3D, textVerification, textVerificationFeatureType } from './const';
import { ALERT_TYPES, aoiAlert } from './alert';
import { conveyorNum, isAOI2DSMT } from './const';
import { getFabricViewportCenter, rotatePoint } from '../viewer/util';


export const backendTimestampToDisplayString = (timestamp) => {
  // ex. 2024-08-12T15:54:58 Pacific Daylight Time
  // Use regex to extract the date and time part, ignoring the timezone part
  const cleanedTimeString = timestamp.match(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}/)[0];
  // Parse the cleaned time string
  // const parsedTime = moment(cleanedTimeString, 'YYYY-MM-DDTHH:mm:ss');
  // Format the parsed time
  // return parsedTime.format('YYYY-MM-DD HH:mm:ss');

  // convert backend's utc to local time
  // let parsedTime = moment.utc(cleanedTimeString).format('YYYY-MM-DDTHH:mm:ss');
  // parsedTime = moment.utc(cleanedTimeString).toDate();
  // return moment(parsedTime).local().format('YYYY-MM-DD HH:mm:ss');

  // 2025/08/18 pcb aoi's backend time will be local time so no need to convert
  return moment(cleanedTimeString).format('YYYY-MM-DD HH:mm:ss');
};

export const backendAutoGenTimeToDisplayString = (timeStr) => {
  if (_.isEmpty(timeStr)) return '';

  // auto generated time in db is utc + 4 hrs and 30 mins
  // need to convert to local time's moment object
  const cleanedTimeString = timeStr.match(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}/)[0];

  // subtract 4 hrs and 30 mins
  // const parsedTime = moment.utc(cleanedTimeString, 'YYYY-MM-DDTHH:mm:ss').subtract(4, 'hours').subtract(30, 'minutes');
  // const parsedTime = moment.utc(cleanedTimeString, 'YYYY-MM-DDTHH:mm:ss');
  // convert from utc to local
  // display hour in 24 hrs format
  // return parsedTime.local().format('YYYY-MM-DD HH:mm:ss');

  // 2025/08/18 pcb aoi's backend time will be local time so no need to convert
  return moment(cleanedTimeString).format('YYYY-MM-DD HH:mm:ss');
};


export const localDayjsToUTCMoment = (localTime) => {
  // Dayjs object to formated utc time string
  return moment(localTime).utc();
};

export const convertBackendTimestampToMoment = (timestamp) => {
  // ex. 2024-08-12T15:54:58 Pacific Daylight Time
  // Use regex to extract the date and time part, ignoring the timezone part
  const cleanedTimeString = timestamp.match(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}/)[0];
  // Parse the cleaned time string
  const parsedTime = moment(cleanedTimeString, 'YYYY-MM-DDTHH:mm:ss');
  return parsedTime;
};

export const getQueryParams = (search) => {
  const params = {};
  const searchParams = new URLSearchParams(search);
  for (const [key, value] of searchParams) {
    params[key] = value;
  }
  return params;
};

export const csvParser = (
  str,
  delimiter,
  botLayerId,
  isIgnoreBotLayer,
  botLayerCol,
  firstRowIdx,
  lastRowIdx,
) => {

  let processedStr = str;
  const lines = str.split('\n');

  const firstFewLines = lines.slice(0, 5).map(line => ({
    content: line,
    columns: line.split(delimiter).length,
    hasDelimiter: line.includes(delimiter)
  }));

  if (firstFewLines.length >= 2) {
    const firstLineColumns = firstFewLines[0].columns;
    const secondLineColumns = firstFewLines[1].columns;

    if (!firstFewLines[0].hasDelimiter || (firstLineColumns === 1 && secondLineColumns > 5)) {
      if (firstRowIdx === 1) {
        firstRowIdx = 2;
      }
    }
  }

  const records = [];
  const parser = parse({
    delimiter,
    skip_empty_lines: true,
    relax_column_count: true
  });
  let curRowIdx = 1;

  parser.on('readable', () => {
    let record;
    while (record = parser.read()) {
      let shouldSkip = false;

      // skip prefix and suffix if defined
      if (_.isInteger(firstRowIdx) && _.isInteger(lastRowIdx) && firstRowIdx >= 1 && lastRowIdx >= firstRowIdx && (curRowIdx < firstRowIdx || curRowIdx > lastRowIdx)) {
        shouldSkip = true;
      } else if (_.isInteger(firstRowIdx) && firstRowIdx >= 1 && curRowIdx < firstRowIdx) {
        shouldSkip = true;
      } else if (_.isInteger(lastRowIdx) && lastRowIdx >= 1 && curRowIdx > lastRowIdx) {
        shouldSkip = true;
      }

      // 递增行计数器
      curRowIdx += 1;

      // 如果需要跳过，则继续下一行
      if (shouldSkip) {
        continue;
      }

      // ignore bot/top layer if enabled
      if (_.isInteger(botLayerCol) && _.isBoolean(isIgnoreBotLayer) && isIgnoreBotLayer && _.get(record, `${botLayerCol}`) === botLayerId) {
        continue;
      } else {
        records.push(record);
      }
    }
  });

  parser.on('error', (err) => {
    console.error('CSV解析错误:', err.message);
  });

  parser.write(processedStr);
  parser.end();

  return { records, originalRecordCount: curRowIdx - 1 };
};export const allTrue = (obj) => {
  if (typeof obj !== 'object' || obj === null) {
      return obj === true;
  }

  for (const value of Object.values(obj)) {
    if (!allTrue(value)) {
        return false;
    }
  }

  return true;
};

export const getDimensionFromTemplateInfo = (t) => {
  if (_.isEmpty(t) || _.isEmpty(_.get(t, 'model.canned_rois', []))) return { width: 0, height: 0 };

  // find min y, max y, min x, max x from rois
  let minx = Infinity;
  let maxx = -Infinity;
  let miny = Infinity;
  let maxy = -Infinity;

  for (const r of _.get(t, 'model.canned_rois', [])) {
    minx = Math.min(minx, _.get(r, 'shape.points[0].x', 0));
    maxx = Math.max(maxx, _.get(r, 'shape.points[1].x', 0));
    miny = Math.min(miny, _.get(r, 'shape.points[0].y', 0));
    maxy = Math.max(maxy, _.get(r, 'shape.points[1].y', 0));
  }

  return { width: maxx - minx, height: maxy - miny };
};

/**
 * return random color by string, same string will return same color
 * @param {String} str
 */
export const getColorByStr = (str) => {
  const hue = Number('0x' + md5(str).toString().substring(0, 10)) % 360;
  const storkeColor = `hsl(${hue}, 100%, 50%)`;
  return storkeColor;
};

export const getMIMETypeByExtension = (ext) => {
  switch (ext) {
    case 'csv':
      return 'text/csv';
    case 'txt':
      return 'text/plain';
    case 'tsv':
      return 'text/tab-separated-values';
    default:
      return 'text/plain';
  }
};

export const toLocalISOString = (date) => {
  if (!date) return '';

  const pad = (num) => num.toString().padStart(2, '0');

  const year = date.getFullYear();
  const month = pad(date.getMonth() + 1); // Months are 0-based
  const day = pad(date.getDate());
  const hours = pad(date.getHours());
  const minutes = pad(date.getMinutes());
  const seconds = pad(date.getSeconds());
  const milliseconds = date.getMilliseconds().toString().padStart(3, '0');

  return `${year}-${month}-${day}T${hours}:${minutes}:${seconds}.${milliseconds}Z`;
};

export const checkIfReadyForRetrain = (modelStatus) => {
  if (_.isEmpty(modelStatus)) return true;
  return _.isEmpty(_.filter(modelStatus, (taskStatus) => !_.includes([
    retrainModelTaskPhaseType.failure,
    retrainModelTaskPhaseType.complete,
    retrainModelTaskPhaseType.invalid,
    retrainModelTaskPhaseType.partialFailure,
  ], taskStatus.phase)));
};

// yes, teach and inference status are separated into two api
export const getCurrentConveyorStatus = async (lazyGetConveyorTeachStatus, lazyGetConveyorInferenceStatus, t) => {
  const teachStatusRes = await lazyGetConveyorTeachStatus();

  if (teachStatusRes.error) {
    console.error('get teach status failed', teachStatusRes.error.message);
    aoiAlert(t('notification.error.getAllConveyorStatus'), ALERT_TYPES.COMMON_ERROR);
    throw new Error('failed to get teach status');
  }

  const inferenceStatusRes = await lazyGetConveyorInferenceStatus();

  if (inferenceStatusRes.error) {
    console.error('get inference status failed', inferenceStatusRes.error.message);
    aoiAlert(t('notification.error.getAllConveyorStatus'), ALERT_TYPES.COMMON_ERROR);
    throw new Error('failed to get inference status');
  }

  // not sure if this is correct
  // check inference status to see which conveyor is running inference
  // then check teach status for which conveyor is used for product programming
  const result = {};

  // slot id starts from 0 but /conveyorSlots's response starts from 1

  for (let i = 0; i < conveyorNum; i++) {
    if (
      _.get(inferenceStatusRes, `data.conveyeor_status.${i}.golden_product_id`, 0) === -1 &&
      _.get(inferenceStatusRes, `data.conveyeor_status.${i}.session_id`, 0) === -1 &&
      _.get(inferenceStatusRes, `data.conveyeor_status.${i}.status`, '') === 'stopped'
    ) {
      // conveyor 1 not running inference
      if (_.get(teachStatusRes, `data.conveyor_map.${i}.available`, false)) {
        // conveyor 0 is idle
        result[`${i}`] = {
          taskType: 'none',
          info: {},
        };
      } else {
        // conveyor 0 is idle
        result[`${i}`] = {
          taskType: _.get(teachStatusRes, `data.conveyor_map.${i}.intent`, 'none') === 'inspection' ? 'inspecting' : 'programming',
          info: _.get(teachStatusRes, `data.conveyor_map.${i}`, {}),
        };
      }
    } else {
      result[`${i}`] = {
        taskType: 'inspecting',
        info: _.get(inferenceStatusRes, `data.conveyeor_status.${i}`, {}),
        isContinuous: _.get(inferenceStatusRes, `data.conveyeor_status.${i}.status`, '') === 'continuous',
      };
    }
  }

  return result;
};

export const sleep = async (ms) => {
  return new Promise((resolve) => setTimeout(resolve, ms));
};

export const getFeatureTypeByLineItemName = (lineItemParams) => {
  const names = _.keys(lineItemParams);

  if (_.includes(names, mountingInspection2D) || _.includes(names, mountingInspection3D)) return mountingFeatureType;
  if (_.includes(names, leadInspection2D) || _.includes(names, leadInspection3D) || _.includes(names, leadInspection2DBase)) return leadFeatureType;
  if (_.includes(names, solderInspection3D) || _.includes(names, solderInspection2D)) return solderFeatureType;
  if (_.includes(names, textVerification)) return textVerificationFeatureType;
  if (_.includes(names, barcodeScanner)) return barcodeScannerFeatureType;
};

export const getAgentParamTypeNValueByParam = (param, agentName, agentParamName) => {
  // console.log('param', param, agentName, agentParamName);

  if (!_.isEmpty(_.get(param, 'param_float', null))) {
    return {
      type: 'float',
      value: _.get(param, 'param_float.value', 0),
      min: _.get(param, 'param_float.min', 0),
      max: _.get(param, 'param_float.max', 0),
    };
  }
  if (!_.isEmpty(_.get(param, 'param_int', null))) {
    return {
      type: 'int',
      value: _.get(param, 'param_int.value', 0),
      min: _.get(param, 'param_int.min', 0),
      max: _.get(param, 'param_int.max', 0),
    };
  }
  if (_.isBoolean(_.get(param, 'param_bool', null))) {
    return {
      type: 'bool',
      value: _.get(param, 'param_bool', false),
    };
  }
  if (_.isString(_.get(param, 'param_string', null))) {
    return {
      type: 'string',
      value: _.get(param, 'param_string', ''),
    };
  }
  if (_.includes(
    doubleSidedSliderAgentParams,
    `${agentName}.${agentParamName}`
  )) {
    return {
      type: 'minMaxRange',
      okMin: _.get(param, 'param_range.ok_min', 0),
      okMax: _.get(param, 'param_range.ok_max', 0),
      min: _.get(param, 'param_range.min', 0),
      max: _.get(param, 'param_range.max', 0),
    };
  }
  if (!_.isEmpty(_.get(param, 'param_range', null))) {
    return {
      type: 'minMaxRange',
      okMin: _.get(param, 'param_range.ok_min', 0),
      okMax: _.get(param, 'param_range.ok_max', 0),
      min: _.get(param, 'param_range.min', 0),
      max: _.get(param, 'param_range.max', 0),
    };
  }
};

export const applyTemplateString = (template, params) => {
  // replace ${paramName} with params[paramName] in template
  // ex. "Hello ${name}, your age is ${age}" with params = { name: 'John', age: 30 }
  return template.replace(/\$\{(\w+)\}/g, (match, paramName) => {
    // if paramName is in params, return its value, otherwise return the match
    return _.has(params, paramName) ? params[paramName] : match;
  });
};

export const orderAgentParams = (paramName) => {
  // mask sure polarity check and its roi are ordered together
  if (paramName === polarityCheckThreshold) return 8;
  if (paramName === polarityRoi) return 9;
  if (_.includes(paramName, '_roi')) return 10;
  return 1;
};

export const is2DLeadColorAgentParams = (agentName, agentParamName) => {
  return agentName === leadInspection2D && _.includes(lead2DColorAgentParamNames, agentParamName);
};

export const is2DLeadRangeAgentParams = (agentName, agentParamName) => {
  // return agentName === leadInspection2D &&
};

export const is2DSolderColorAgentParams = (agentName, agentParamName) => {
  return agentName === solderInspection2D && _.includes(solder2DColorAgentParamNames, agentParamName);
};

export const twoDLeadGapAgentParamCheck = (agentName, agentParamName) => {
  return (_.includes(agentParamStatsList[leadGapFeatureType][leadInspection2DBase], agentParamName) && agentName === leadInspection2DBase) || agentName !== leadInspection2DBase;
};

export const readFileWithEncoding = async (file) => {
  const buffer = await new Promise((resolve) => {
    const reader = new FileReader();
    reader.onload = () => resolve(reader.result);
    reader.readAsArrayBuffer(file.slice(0, 1024));
  });

  const bytes = new Uint8Array(buffer);


  if (bytes.length >= 3 && bytes[0] === 0xEF && bytes[1] === 0xBB && bytes[2] === 0xBF) {
    // UTF-8 BOM
    return readFileAsText(file, 'UTF-8');
  }

  if (bytes.length >= 2 && bytes[0] === 0xFF && bytes[1] === 0xFE) {
    // UTF-16 LE BOM
    return readFileAsText(file, 'UTF-16LE');
  }

  if (bytes.length >= 2 && bytes[0] === 0xFE && bytes[1] === 0xFF) {
    // UTF-16 BE BOM
    return readFileAsText(file, 'UTF-16BE');
  }


  const isLikelyGB = detectGB18030(bytes);

  if (isLikelyGB) {
    try {
      return await readFileAsText(file, 'GB18030');
    } catch (error) {
      console.warn('GB18030 decoding failed, fallback to UTF-8:', error);
      return readFileAsText(file, 'UTF-8');
    }
  }

  return readFileAsText(file, 'UTF-8');
};

const readFileAsText = async (file, encoding = 'UTF-8') => {
  if (encoding === 'UTF-8' || encoding === 'UTF-16LE' || encoding === 'UTF-16BE') {
    return new Promise((resolve) => {
      const reader = new FileReader();
      reader.onload = () => resolve(reader.result);
      reader.readAsText(file, encoding);
    });
  }

  const buffer = await new Promise((resolve) => {
    const reader = new FileReader();
    reader.onload = () => resolve(reader.result);
    reader.readAsArrayBuffer(file);
  });

  try {
    const supportedEncodings = ['GB18030', 'GBK', 'GB2312'];
    let decoder;

    if (supportedEncodings.includes(encoding.toUpperCase())) {
      try {
        decoder = new TextDecoder(encoding);
      } catch (e) {
        if (encoding.toUpperCase() === 'GB18030') {
          try {
            decoder = new TextDecoder('GBK');
          } catch (e2) {
            console.warn('Browser does not support GB18030/GBK encoding, fallback to UTF-8');
            decoder = new TextDecoder('UTF-8');
          }
        } else {
          decoder = new TextDecoder('UTF-8');
        }
      }
    } else {
      decoder = new TextDecoder(encoding);
    }

    return decoder.decode(buffer);
  } catch (error) {
    console.warn(`Failed to decode with ${encoding}, fallback to UTF-8:`, error);
    const decoder = new TextDecoder('UTF-8');
    return decoder.decode(buffer);
  }
};

const detectGB18030 = (bytes) => {
  let gbLikeCharCount = 0;
  let totalBytes = 0;
  let chineseCharCount = 0;

  for (let i = 0; i < bytes.length; i++) {
    const byte = bytes[i];
    totalBytes++;

    if (byte <= 0x7F) {
      continue;
    }

    if (byte >= 0x81 && byte <= 0xFE && i + 1 < bytes.length) {
      const nextByte = bytes[i + 1];

      if ((nextByte >= 0x40 && nextByte <= 0x7E) || (nextByte >= 0x80 && nextByte <= 0xFE)) {
        gbLikeCharCount++;

        if ((byte >= 0xB0 && byte <= 0xF7) && (nextByte >= 0xA1 && nextByte <= 0xFE)) {
          chineseCharCount++;
        }

        i++;
      }
    }
  }

  const nonAsciiBytes = bytes.filter(b => b > 0x7F).length;

  if (nonAsciiBytes === 0) return false;

  const gbRatio = gbLikeCharCount / (nonAsciiBytes / 2);
  const chineseRatio = chineseCharCount / gbLikeCharCount;

  return gbRatio > 0.6 || (gbRatio > 0.3 && chineseRatio > 0.2);
};

// FSM state enums
const DigitFsmState = {
  START: 0,
  TERMINATE: 1,
  DIGIT: 2,
  DOT: 3,
  X: 4,
};

const WordFsmState = {
  START: 0,
  TERMINATE: 1,
  ALPHA: 2,
};

/**
 * Attempts to match a numeric token (including optional dot or 'x' patterns)
 * starting at `start`. Returns the matched substring or "".
 */
function matchDigit(searchTerm, start) {
  if (start >= searchTerm.length) return "";

  let result = "";
  let ptr = start;
  let state = DigitFsmState.START;

  while (state !== DigitFsmState.TERMINATE) {
    switch (state) {
      case DigitFsmState.START:
        if (/\d/.test(searchTerm[ptr])) {
          result += searchTerm[ptr++];
          state = DigitFsmState.DIGIT;
        } else {
          state = DigitFsmState.TERMINATE;
        }
        break;

      case DigitFsmState.DIGIT:
        if (ptr >= searchTerm.length) {
          state = DigitFsmState.TERMINATE;
        } else if (/\d/.test(searchTerm[ptr])) {
          result += searchTerm[ptr++];
        } else if (searchTerm[ptr].toLowerCase() === 'x') {
          state = DigitFsmState.X;
        } else if (searchTerm[ptr] === '.') {
          state = DigitFsmState.DOT;
        } else {
          state = DigitFsmState.TERMINATE;
        }
        break;

      case DigitFsmState.DOT:
      case DigitFsmState.X:
        if (ptr + 1 >= searchTerm.length) {
          state = DigitFsmState.TERMINATE;
        } else if (/\d/.test(searchTerm[ptr + 1])) {
          // include the dot or 'x'
          result += searchTerm[ptr++];
          state = DigitFsmState.DIGIT;
        } else {
          state = DigitFsmState.TERMINATE;
        }
        break;

      default:
        throw new Error("Invalid FSM state");
    }
  }

  return searchTerm.slice(start, ptr);
}

/**
 * Attempts to match an alphabetic word token starting at `start`.
 * Returns the matched substring or "".
 */
function matchWord(searchTerm, start) {
  if (start >= searchTerm.length) return "";

  let result = "";
  let ptr = start;
  let state = WordFsmState.START;

  while (state !== WordFsmState.TERMINATE) {
    switch (state) {
      case WordFsmState.START:
        if (/[A-Za-z]/.test(searchTerm[ptr])) {
          result += searchTerm[ptr++];
          state = WordFsmState.ALPHA;
        } else {
          state = WordFsmState.TERMINATE;
        }
        break;

      case WordFsmState.ALPHA:
        if (ptr >= searchTerm.length) {
          state = WordFsmState.TERMINATE;
        } else if (/[A-Za-z]/.test(searchTerm[ptr])) {
          result += searchTerm[ptr++];
        } else {
          state = WordFsmState.TERMINATE;
        }
        break;

      default:
        throw new Error("Invalid FSM state");
    }
  }

  return searchTerm.slice(start, ptr);
}

/**
 * Splits the input string into an array of digit- and word-based tokens.
 */
function tokenize(searchTerm) {
  if (!searchTerm) return [];

  const result = [];
  let i = 0;

  while (i < searchTerm.length) {
    const ch = searchTerm[i];
    if (!/[A-Za-z0-9]/.test(ch)) {
      i++;
      continue;
    }

    const digit = matchDigit(searchTerm, i);
    if (digit) {
      result.push(digit);
      i += digit.length;
      continue;
    }

    const word = matchWord(searchTerm, i);
    if (word) {
      result.push(word);
      i += word.length;
      continue;
    }

    console.warn(`Unknown state at ${searchTerm}@${i}`);
    i++;
  }

  return result;
}

/**
 * Builds a space‑separated string of unique tokens from a list of aliases,
 * ensuring the longest alias’s tokens come first.
 */
export const buildSearchTerms = (aliases) => {
  if (!aliases || aliases.length === 0) return "";

  // find the longest alias
  const longest = aliases.reduce((a, b) => (b.length > a.length ? b : a), "");

  const initialTokens = tokenize(longest);
  const seen = new Set(initialTokens);
  let result = initialTokens.join(" ");

  for (const alias of aliases) {
    if (alias === longest) continue;
    const tokens = tokenize(alias);
    for (const token of tokens) {
      if (!seen.has(token)) {
        result += " " + token;
        seen.add(token);
      }
    }
  }

  return result;
}

export const blurOcr = (
  predictedText,
  expectedText,
  highlightPositions = [],
) => {
  const predicted = predictedText.split('').map((ch, idx) => ({
    char: ch,
    highlight: highlightPositions.includes(idx),
  }));

  const expected = expectedText.split('').map((ch, idx) => ({
    char: ch,
    highlight: highlightPositions.includes(idx),
  }));

  return { predicted, expected };
};

export const isMounting3DExtendedRoiAgent = (lintItemName, agentParamName) => {
  if (lintItemName !== mountingInspection3D) return false;
  return _.includes(mounting3DExtendedRoiAgentParamNames, agentParamName);
};

export const isProfileRoiAgent = (agentParamName) => {
  return _.includes(profileRoiAgentParamNames, agentParamName);
};

export const strContainsChineseChar = (str) => {
  return /[\u4e00-\u9fa5]/.test(str);
};

export const removeChineseChar = (str) => {
  return str.replace(/[\u4e00-\u9fa5]/g, '');
};

export const destroyFabricCanvas = (fabricCanvas) => {
  if (!fabricCanvas) return;

  fabricCanvas.remove(...fabricCanvas.getObjects());
  fabricCanvas.clear();

  // 1) Try to release WebGL VRAM if Fabric filters were used
  try {
    const fb = fabric.filterBackend;
    const gls = new Set();
    if (fb?.gl) gls.add(fb.gl);
    if (fb?.textureBackend?.gl) gls.add(fb.textureBackend.gl);
    if (fb?.tileBackend?.gl) gls.add(fb.tileBackend.gl);

    for (const gl of gls) {
      const ext = gl.getExtension("WEBGL_lose_context");
      if (ext) ext.loseContext();
    }
  } catch (e) {}

  // 2) Dispose Fabric instance (objects, events, timers)
  try {
    fabricCanvas.dispose();
  } catch (_) {}
}

export const getRunningTaskGoldenProductIds = async (lazyGetConveyorInferenceStatus) => {
  const inferenceStatusRes = await lazyGetConveyorInferenceStatus();

  if (inferenceStatusRes.error) {
    console.error('get inference status failed', inferenceStatusRes.error.message);
    return [];
  }

  return _.chain(inferenceStatusRes.data.conveyeor_status)
    .filter(i => i.status !== 'stopped')
    .map(i => i.golden_product_id)
    .uniq()
    .value();
};

export const getInsepctionTimeInMs = (inspectionDto) => {
  // parse time_finished and time started ex. 2025-08-27T11:36:55.057
  try {
    const timeFinished = moment(inspectionDto.time_finished);
    const timeStarted = moment(inspectionDto.time_started);
    if (timeFinished.isValid() && timeStarted.isValid()) {
      return timeFinished.diff(timeStarted);
    }
  } catch (e) {
    console.error('parse inspection time failed', e);
    return -1;
  }
};

export const getCurrentCanvasViewportArrayIndex = (fcanvasRef, arrayRegisteration) => {
  // get the array index of the current viewport center
  if (!fcanvasRef.current || !arrayRegisteration) return null;

  const vpCenter = getFabricViewportCenter(fcanvasRef.current);
  const selectionRoiPMin = {
    x: _.get(arrayRegisteration, 'selection_roi.points[0].x', 0),
    y: _.get(arrayRegisteration, 'selection_roi.points[0].y', 0),
  };
  const selectionWidth = _.get(arrayRegisteration, 'selection_roi.points[1].x', 0) - selectionRoiPMin.x;
  const selectionHeight = _.get(arrayRegisteration, 'selection_roi.points[1].y', 0) - selectionRoiPMin.y;

  const localVpCenter = {
    x: vpCenter.x - selectionRoiPMin.x,
    y: vpCenter.y - selectionRoiPMin.y,
  };

  const arrayTransforms = _.get(arrayRegisteration, 'array_transforms', []);
  for (const transform of arrayTransforms) {
    const { translation, rotation } = transform;
    const translationX = _.get(translation, 'x', 0);
    const translationY = _.get(translation, 'y', 0);
    const rotationAngle = _.get(rotation, 'angle', 0);

    // Calculate the center of the selection ROI in its local coordinate system
    const localSelectionCenter = {
      x: selectionWidth / 2,
      y: selectionHeight / 2
    };

    // Apply translation (translation is based on selectionRoi's pMin in local coord)
    const translatedCenter = {
      x: selectionRoiPMin.x + translationX + localSelectionCenter.x,
      y: selectionRoiPMin.y + translationY + localSelectionCenter.y
    };

    // Apply rotation
    const rotatedCenter = rotatePoint(translatedCenter, rotationAngle, selectionRoiPMin);
    const localRotatedCenter = { x: rotatedCenter.x - selectionRoiPMin.x, y: rotatedCenter.y - selectionRoiPMin.y }; // local coord center of the rotated ROI
    const localVpCenterInLocalRotatedCenterCoord = { x: localVpCenter.x - localRotatedCenter.x, y: localVpCenter.y - localRotatedCenter.y }; // local coord center of the viewport center in the local coord of the rotated ROI

    // Check if the viewport center is within the rotated and translated selection ROI
    if (
      localVpCenterInLocalRotatedCenterCoord.x >= -selectionWidth / 2 &&
      localVpCenterInLocalRotatedCenterCoord.x <= selectionWidth / 2 &&
      localVpCenterInLocalRotatedCenterCoord.y >= -selectionHeight / 2 &&
      localVpCenterInLocalRotatedCenterCoord.y <= selectionHeight / 2 // check if the viewport center is within the rotated and translated selection ROI in local coord of the rotated ROI
    ) {
      return transform.array_index;
    }
  }

  return null;
};

export const getArrayIndexByPoint = (point, arrayRegisteration) => {
  if (!arrayRegisteration) return null;

  const selectionRoiPMin = {
    x: _.get(arrayRegisteration, 'selection_roi.points[0].x', 0),
    y: _.get(arrayRegisteration, 'selection_roi.points[0].y', 0),
  };
  const selectionWidth = _.get(arrayRegisteration, 'selection_roi.points[1].x', 0) - selectionRoiPMin.x;
  const selectionHeight = _.get(arrayRegisteration, 'selection_roi.points[1].y', 0) - selectionRoiPMin.y;

  const arrayTransforms = _.get(arrayRegisteration, 'array_transforms', []);
  for (const transform of arrayTransforms) {
    const { translation, rotation } = transform;
    const translationX = _.get(translation, 'x', 0);
    const translationY = _.get(translation, 'y', 0);
    const rotationAngle = _.get(rotation, 'angle', 0);

    // Calculate the center of the selection ROI in its local coordinate system
    const localSelectionCenter = {
      x: selectionWidth / 2,
      y: selectionHeight / 2
    };

    // Apply translation (translation is based on selectionRoi's pMin in local coord)
    const translatedCenter = {
      x: selectionRoiPMin.x + translationX + localSelectionCenter.x,
      y: selectionRoiPMin.y + translationY + localSelectionCenter.y
    };

    const tmpRect = new fabric.Rect({
      left: translatedCenter.x - selectionWidth / 2,
      top: translatedCenter.y - selectionHeight / 2,
      width: selectionWidth,
      height: selectionHeight,
    });

    // Apply rotation
    tmpRect.rotate(rotationAngle);
    if (!_.isArray(point)) {
      if (tmpRect.containsPoint(point)) return transform.array_index;
    } else {
      if (point.every(p => {
        return tmpRect.containsPoint(p);
      })) {
        return transform.array_index;
      }
    }
  }

  return null;
};