import React, { useEffect, useState, useRef } from "react";
import { useTranslation } from "react-i18next";
import { useSearchParams } from "react-router-dom";
import { useGetInspectedComponentQuery, useGetSingleInspectionQuery } from "../../services/inference";
import { backendAutoGenTimeToDisplayString } from "../../common/util";
import _ from 'lodash';
import { localStorageKeys, serverHost } from "../../common/const";
import { t } from "i18next";


const InspectionDetailExport = (props) => {
  const [searchParams] = useSearchParams();
  const ipcProductId = searchParams.get('ipc-product-id');
  const ipcSessionId = searchParams.get('ipc-session-id');
  const goldenProductId = searchParams.get('golden-product-id');
  const ipcProductName = searchParams.get('ipc-product-name');
  const inspectionTime = searchParams.get('inspection-time');

  const { t } = useTranslation();
  const printableRef = useRef(null);

  const [isPassed, setIsPassed] = useState(false);

  const { data: inspectedComponents } = useGetInspectedComponentQuery({
    inspected_product_id: ipcProductId,
    step: 0,
    prediction_ok: false, // for now we only store prediction ng's img
  }, {
    refetchOnMountOrArgChange: true,
  });
  const { data: inferenceInfo } = useGetSingleInspectionQuery({ product_id: ipcProductId }, { refetchOnMountOrArgChange: true });

  const handleContextMenu = (e) => {
    // Allow default browser context menu which includes "Print"
    // No need to prevent default - browser will show its context menu
  };

  useEffect(() => {
    if (!inferenceInfo) return;

    // check if subunits is empty
    if (_.isEmpty(_.get(inferenceInfo, 'subunits', []))) {
      // no array group then check if defective component count is 0
      setIsPassed(_.get(inferenceInfo, 'defective_component_count', 0) === 0);
    } else {
      // check if a subunit is not wasted and failure ratio is 0
      setIsPassed(_.some(_.get(inferenceInfo, 'subunits', []), su => !su.wasted && su.failure_ratio === 0));
    }
  }, [inferenceInfo]);

  return (
    <>
      <style>{`
        @media print {
          body * {
            visibility: hidden;
          }
          .printable-section, .printable-section * {
            visibility: visible;
          }
          .printable-section {
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
          }
        }
      `}</style>
      <div
        className="printable-section flex items-start flex-[1_0_0] self-stretch px-8 py-6 bg-[#fff] cursor-pointer"
        style={{
          color: '#333'
        }}
        ref={printableRef}
        onContextMenu={handleContextMenu}
      >
        <div className="flex w-[796px] flex-col items-start gap-8 border-[color:var(--default-Gray-6,#F2F2F2)] rounded-[5.203px] border-[1.301px] border-solid">
        <div className="flex items-start gap-4 self-stretch border-[color:var(--default-Gray-6,#F2F2F2)] border-0 border-solid px-3 py-4 flex-1">
          <div className="flex flex-col items-start gap-4 self-stretch flex-1">
            <div className="flex flex-col items-start gap-1 self-stretch">
              <div className="flex items-center gap-6 self-stretch border-b-[color:var(--default-Gray-6,#F2F2F2)] [background:rgba(0,0,0,0.03)] px-2 py-0 border-b border-solid">
                <div className="flex flex-col justify-center items-start flex-[1_0_0] px-0 py-1 rounded-[2px_2px_0_0]">
                  <span className="font-source text-sm font-semibold leading-[150%] capitalize">
                    {t('dataAnalysisReport.productId')}
                  </span>
                </div>
                <div className="flex flex-col justify-center items-start flex-[1_0_0] px-0 py-1 rounded-[2px_2px_0_0]">
                  <span className="font-source text-sm font-semibold leading-[150%] capitalize">
                    {ipcProductId}
                  </span>
                </div>
              </div>
              <div className="flex items-center gap-6 self-stretch border-b-[color:var(--default-Gray-6,#F2F2F2)] [background:rgba(0,0,0,0.03)] px-2 py-0 border-b border-solid">
                <div className="flex flex-col justify-center items-start flex-[1_0_0] px-0 py-1 rounded-[2px_2px_0_0]">
                  <span className="font-source text-sm font-semibold leading-[150%] capitalize">
                    {t('dataAnalysisReport.pcbaName')}
                  </span>
                </div>
                <div className="flex flex-col justify-center items-start flex-[1_0_0] px-0 py-1 rounded-[2px_2px_0_0]">
                  <span className="font-source text-sm font-semibold leading-[150%] capitalize">
                    {ipcProductName}
                  </span>
                </div>
              </div>
              <div className="flex items-center gap-6 self-stretch border-b-[color:var(--default-Gray-6,#F2F2F2)] px-2 py-0 border-b border-solid">
                <div className="flex flex-col justify-center items-start flex-[1_0_0] px-0 py-1 rounded-[2px_2px_0_0]">
                  <span className="font-source text-sm font-semibold leading-[150%] capitalize">
                    {t('dataAnalysisReport.inspectionTime')}
                  </span>
                </div>
                <div className="flex flex-col justify-center items-start flex-[1_0_0] px-0 py-1 rounded-[2px_2px_0_0]">
                  <span className="font-source text-sm font-semibold leading-[150%] capitalize">
                    {(inspectionTime ? backendAutoGenTimeToDisplayString(inspectionTime) : '')}
                  </span>
                </div>
              </div>
              <div className="flex items-center gap-6 self-stretch border-b-[color:var(--default-Gray-6,#F2F2F2)] px-2 py-0 border-b border-solid">
                <div className="flex flex-col justify-center items-start flex-[1_0_0] px-0 py-1 rounded-[2px_2px_0_0]">
                  <span className="font-source text-sm font-semibold leading-[150%] capitalize">
                    {t('dataAnalysisReport.predictedResult')}
                  </span>
                </div>
                <div className="flex flex-col justify-center items-start flex-[1_0_0] px-0 py-1 rounded-[2px_2px_0_0]">
                  <span className="font-source text-sm font-semibold leading-[150%] capitalize">
                    {isPassed ? t('dataAnalysisReport.good') : t('dataAnalysisReport.failed')}
                  </span>
                </div>
              </div>
              <div className="flex items-center gap-6 self-stretch border-b-[color:var(--default-Gray-6,#F2F2F2)] px-2 py-0 border-b border-solid">
                <div className="flex flex-col justify-center items-start flex-[1_0_0] px-0 py-1 rounded-[2px_2px_0_0]">
                  <span className="font-source text-sm font-semibold leading-[150%] capitalize">
                    {t('dataAnalysisReport.arrayBoardCount')}
                  </span>
                </div>
                <div className="flex flex-col justify-center items-start flex-[1_0_0] px-0 py-1 rounded-[2px_2px_0_0]">
                  <span className="font-source text-sm font-semibold leading-[150%] capitalize">
                    {_.get(inferenceInfo, 'subunits', []).length}
                  </span>
                </div>
              </div>
              <div className="flex items-center gap-6 self-stretch border-b-[color:var(--default-Gray-6,#F2F2F2)] px-2 py-0 border-b border-solid">
                <div className="flex flex-col justify-center items-start flex-[1_0_0] px-0 py-1 rounded-[2px_2px_0_0]">
                  <span className="font-source text-sm font-semibold leading-[150%] capitalize">
                    {t('dataAnalysisReport.totalComponent')}
                  </span>
                </div>
                <div className="flex flex-col justify-center items-start flex-[1_0_0] px-0 py-1 rounded-[2px_2px_0_0]">
                  <span className="font-source text-sm font-semibold leading-[150%] capitalize">
                    {_.get(inferenceInfo, 'total_component_count', 0)}
                  </span>
                </div>
              </div>
              <div className="flex items-center gap-6 self-stretch border-b-[color:var(--default-Gray-6,#F2F2F2)] px-2 py-0 border-b border-solid">
                <div className="flex flex-col justify-center items-start flex-[1_0_0] px-0 py-1 rounded-[2px_2px_0_0]">
                  <span className="font-source text-sm font-semibold leading-[150%] capitalize">
                    {t('dataAnalysisReport.alertedComponentCount')}
                  </span>
                </div>
                <div className="flex flex-col justify-center items-start flex-[1_0_0] px-0 py-1 rounded-[2px_2px_0_0]">
                  <span className="font-source text-sm font-semibold leading-[150%] capitalize">
                    {_.get(inferenceInfo, 'defective_component_count', 0)}
                  </span>
                </div>
              </div>
              <div className="flex items-center gap-6 self-stretch border-b-[color:var(--default-Gray-6,#F2F2F2)] px-2 py-0 border-b border-solid">
                <div className="flex flex-col justify-center items-start flex-[1_0_0] px-0 py-1 rounded-[2px_2px_0_0]">
                  <span className="font-source text-sm font-semibold leading-[150%] capitalize">
                    {t('dataAnalysisReport.falsePositiveComponentCount')}
                  </span>
                </div>
                <div className="flex flex-col justify-center items-start flex-[1_0_0] px-0 py-1 rounded-[2px_2px_0_0]">
                  <span className="font-source text-sm font-semibold leading-[150%] capitalize">
                    {_.get(inferenceInfo, 'defective_component_count', 0) - _.get(inferenceInfo, 'feedback_ng_component_count', 0)}
                  </span>
                </div>
              </div>
            </div>
            {/* <div className="flex flex-col items-start gap-1 self-stretch">
            </div> */}
          </div>
        </div>
        <div
          className="flex flex-col items-start self-stretch rounded border border-[color:var(--default-Gray-6,#F2F2F2)] border-solid"
        >
          <div className="flex items-center self-stretch p-2">
            <span className="font-source text-sm font-normal leading-[150%]">
              {t('dataAnalysisReport.alertedComponent')}:
            </span>
          </div>
          <div className="flex items-center gap-4 self-stretch border-b-[color:var(--default-Gray-6,#F2F2F2)] [background:rgba(0,0,0,0.03)] px-2 py-0 border-b border-solid">
            <div className="flex w-[120px] items-center gap-[20.81px]">
              <span className="flex-[1_0_0] font-source text-sm font-normal leading-[150%]">
                {t('dataAnalysisReport.image')}
              </span>
            </div>
            <div className="flex h-[41.621px] items-center gap-2 flex-[1_0_0]">
              <span className="flex-[1_0_0] font-source text-sm font-normal leading-[150%]">
                {t('dataAnalysisReport.designator')}
              </span>
              <div className="w-[1.301px] h-[41.621px] [background:#F2F2F2]" />
              <span className="flex-[1_0_0] font-source text-sm font-normal leading-[150%]">
                {t('productDefine.partNo')}
              </span>
              <div className="w-[1.301px] h-[41.621px] [background:#F2F2F2]" />
              <span className="flex-[1_0_0] font-source text-sm font-normal leading-[150%]">
                {t('dataAnalysisReport.arrayBoardIndex')}
              </span>
              <div className="w-[1.301px] h-[41.621px] [background:#F2F2F2]" />
              <span className="flex-[1_0_0] font-source text-sm font-normal leading-[150%]">
                {t('dataAnalysisReport.status')}
              </span>
              <div className="w-[1.301px] h-[41.621px] [background:#F2F2F2]" />
              <span className="flex-[1_0_0] font-source text-sm font-normal leading-[150%]">
                {t('dataAnalysisReport.ngType')}
              </span>
              <div className="w-[1.301px] h-[41.621px] [background:#F2F2F2]" />
              <span className="flex-[1_0_0] font-source text-sm font-normal leading-[150%]">
                {t('dataAnalysisReport.predictedErrorType')}
              </span>
            </div>
          </div>
          <div className="flex flex-col items-start self-stretch">
            {_.map(_.filter(inspectedComponents, rc => !rc.passing), (rc, idx) => (
              <InspectedComponentRow
                key={idx}
                inspectedComponent={rc}
              />
            ))}
          </div>
        </div>
      </div>
    </div>
    </>
  );
};

const InspectedComponentRow = (props) => {
  const { inspectedComponent } = props;

  if (!inspectedComponent) return null;

  const [url, setUrl] = useState('');

  useEffect(() => {
    let tmpUrl = `${serverHost}/blob?type=image`;
    tmpUrl += `&color_uri=${encodeURIComponent(_.get(inspectedComponent, 'color_map_uri', ''))}`;
    tmpUrl += `&depth_uri=${encodeURIComponent(_.get(inspectedComponent, 'depth_map_uri', ''))}`;
    tmpUrl += `&t=${Date.now().valueOf()}`;
    setUrl(tmpUrl);
    
  }, [inspectedComponent]);

  return (
    <div
      className="flex items-center gap-4 self-stretch border-b-[color:var(--default-Gray-6,#F2F2F2)] px-2 py-0 rounded-[5.203px] border-b-[1.301px] border-solid"
    >
      <div className="flex items-center gap-[13.007px] px-0 py-[5.203px]">
        <img
          src={url}
          alt="component"
          className="w-[120px] h-[120px] object-contain"
        />
      </div>
      <div className="flex items-center gap-2 flex-[1_0_0] self-stretch">
        <span
          className="flex-[1_0_0] font-source text-sm font-normal leading-[150%] overflow-hidden overflow-ellipsis whitespace-nowrap"
          title={inspectedComponent.designator}
        >
          {inspectedComponent.designator}
        </span>
        <div className="w-[1.301px] h-[130.405px] [background:#F2F2F2]" />
        <span
          className="flex-[1_0_0] font-source text-sm font-normal leading-[150%] overflow-hidden overflow-ellipsis whitespace-nowrap"
          title={inspectedComponent.part_no}
        >
          {_.isEmpty(inspectedComponent.part_no) ? 'N/A' : inspectedComponent.part_no}
        </span>
        <div className="w-[1.301px] h-[130.405px] [background:#F2F2F2]" />
        <span className="flex-[1_0_0] font-source text-sm font-normal leading-[150%] overflow-hidden overflow-ellipsis whitespace-nowrap">
          {inspectedComponent.array_index}
        </span>
        <div className="w-[1.301px] h-[130.405px] [background:#F2F2F2]" />
        <span className="flex-[1_0_0] font-source text-sm font-normal leading-[150%] overflow-hidden overflow-ellipsis whitespace-nowrap">
          {inspectedComponent.passing ? 'OK' : 'NG'}
        </span>
        <div className="w-[1.301px] h-[130.405px] [background:#F2F2F2]" />
        <span className="flex-[1_0_0] font-source text-sm font-normal leading-[150%] overflow-hidden overflow-ellipsis whitespace-nowrap">
          {_.isEmpty(inspectedComponent.feedback_error_type) ? 'N/A' : t(`inferenceErrorType.${inspectedComponent.feedback_error_type}`)}
        </span>
        <div className="w-[1.301px] h-[130.405px] [background:#F2F2F2]" />
        <span className="flex-[1_0_0] font-source text-sm font-normal leading-[150%] overflow-hidden overflow-ellipsis whitespace-nowrap">
          {_.isEmpty(inspectedComponent.predicted_error_type) ? 'N/A' : t(`inferenceErrorType.${inspectedComponent.predicted_error_type}`)}
        </span>
      </div>
    </div>
  );
};

export default InspectionDetailExport;