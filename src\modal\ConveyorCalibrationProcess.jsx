import React, { useState } from 'react';
import { CustomModal } from '../common/styledComponent';
import { useTranslation } from 'react-i18next';
import { Button, InputNumber } from 'antd';
import { ALERT_TYPES, aoiAlert } from '../common/alert';
import _ from 'lodash';
import { useCameraMotionCalibrationMutation, useLazyGetCameraSonsorConfigQuery } from '../services/camera';
import { useDispatch } from 'react-redux';
import { setContainerLvlLoadingMsg, setIsContainerLvlLoadingEnabled } from '../reducer/setting';


const ConveyorCalibrationProcess = (props) => {
  const {
    isOpened,
    setIsOpened,
  } = props;
  
  const dispatch = useDispatch();

  const { t } = useTranslation();

  const [isShowingAccuracy, setIsShowingAccuracy] = useState(false);
  const [accuracyResult, setAccuracyResult] = useState(null);
  const [boardWidthMM, setBoardWidthMM] = useState(1);
  const [boardHeightMM, setBoardHeightMM] = useState(1);
  const [exposureTime, setExposureTime] = useState(1);

  const [cameraMotionCalibration] = useCameraMotionCalibrationMutation();
  const [getCameraSensorConfigs] = useLazyGetCameraSonsorConfigQuery();

  const handleSubmit = async (
    boardWidthMM,
    boardHeightMM,
    exposureTime,
  ) => {
    dispatch(setIsContainerLvlLoadingEnabled(true));
    dispatch(setContainerLvlLoadingMsg(t('loader.cameraMotionCalibration')));

    const res = await getCameraSensorConfigs();

    if (res.error) {
      aoiAlert(t('notification.error.getCameraConfigs'), ALERT_TYPES.COMMON_ERROR);
      console.error('getCameraSensorConfigs error:', _.get(res, 'error.message', ''));
      dispatch(setIsContainerLvlLoadingEnabled(false));
      dispatch(setContainerLvlLoadingMsg(''));
      return;
    }

    let sensorConfig = _.cloneDeep(_.get(res, 'data', {}));
    sensorConfig = _.set(sensorConfig, 'sensors[0].camera_configs[0].config_2d.exposure_time', exposureTime);

    const res1 = await cameraMotionCalibration({
      sensor_config: sensorConfig,
      board_width_mm: boardWidthMM,
      board_height_mm: boardHeightMM,
    });

    if (res1.error) {
      aoiAlert(t('notification.error.cameraMotionCalibrationFailed'), ALERT_TYPES.COMMON_ERROR);
      console.error('cameraMotionCalibration error:', _.get(res1, 'error.message', ''));
      dispatch(setIsContainerLvlLoadingEnabled(false));
      dispatch(setContainerLvlLoadingMsg(''));
      return;
    }

    setAccuracyResult(_.get(res1, 'data.accuracy', null));
    setIsShowingAccuracy(true);
    dispatch(setIsContainerLvlLoadingEnabled(false));
    dispatch(setContainerLvlLoadingMsg(''));
  };

  return (
    <CustomModal
      width={656}
      open={isOpened}
      onCancel={() => setIsOpened(false)}
      title={<span className='font-source text-[16px] font-semibold leading-[150%]'>
        {t('settings.conveyorCalibration')}
      </span>}
      footer={null}
    >
      <div className='flex h-[473px] flex-col justify-center items-center gap-6 flex-[1_0_0] [background:#252525] px-4 py-6'>
        {!isShowingAccuracy ?
          <div className='flex flex-col justify-center items-center gap-4 self-stretch p-4'>
            <div className='flex flex-col items-center gap-1 self-stretch'>
              <span className='text-white font-source text-lg font-normal leading-[150%] tracking-[0.54px]'>
                {t('settings.placeLargeCalibrationPlateOnTheTrack')}
              </span>
            </div>
            <div className='flex flex-col items-center gap-2 self-stretch'>
              <div className='flex items-center gap-4 self-stretch flex-1 justify-between'>
                <div className='flex gap-2 items-center self-stretch'>
                  <div className='flex gap-1 items-center self-stretch'>
                    <span className='font-source text-[14px] font-normal leading-[150%] whitespace-nowrap'>
                      {t('autoProgramming.panelWidth')}
                    </span>
                    <div className='flex w-6 h-6 justify-center items-center'>
                      <img
                        src='/icn/panelWidth_color.svg'
                        className='w-3 h-4'
                        alt='panelWidth'
                      />
                    </div>
                  </div>
                  <InputNumber
                    min={1}
                    style={{ width: '100%' }}
                    controls={false}
                    value={boardWidthMM}
                    onChange={(value) => setBoardWidthMM(value)}
                  />
                </div>
                <div className='flex gap-2 items-center self-stretch'>
                  <div className='flex gap-1 items-center self-stretch'>
                    <span className='font-source text-[14px] font-normal leading-[150%] whitespace-nowrap'>
                      {t('autoProgramming.panelLength')}
                    </span>
                    <div className='flex w-6 h-6 justify-center items-center'>
                      <img
                        src='/icn/panelLength_color.svg'
                        className='w-3 h-4'
                        alt='panelLength'
                      />
                    </div>
                  </div>
                  <InputNumber
                    min={1}
                    style={{ width: '100%' }}
                    controls={false}
                    value={boardHeightMM}
                    onChange={(value) => setBoardHeightMM(value)}
                    precision={0}
                  />
                </div>
              </div>
              <div className='flex items-center gap-4 self-stretch flex-1 justify-between'>
                <div className='flex gap-2 items-center self-stretch'>
                  <div className='flex gap-1 items-center self-stretch'>
                    <span className='font-source text-[14px] font-normal leading-[150%] whitespace-nowrap'>
                      {t('productDefine.exposureTime')}:
                    </span>
                  </div>
                  <InputNumber
                    min={1}
                    style={{ width: '100%' }}
                    controls={false}
                    value={exposureTime}
                    onChange={(value) => setExposureTime(value)}
                    precision={0}
                  />
                </div>
                <>
                </>
              </div>
            </div>
            <Button
              type='primary'
              onClick={() => {
                handleSubmit(
                  boardWidthMM,
                  boardHeightMM,
                  exposureTime,
                );
              }}
            >
              <span className='font-source text-[12px] font-semibold leading-[150%]'>
                {t('settings.calibrate')}
              </span>
            </Button>
          </div>
        :
          <div className='flex flex-col justify-center items-center gap-6 flex-[1_0_0] self-stretch [background:#252525] px-4 py-6'>
            <div className='flex justify-center items-center self-stretch px-0 py-0.5'>
              <span className='text-white font-source text-sm font-normal leading-[150%]'>
                {t('settings.accuracyAnalysisResult')}
              </span>
            </div>
            <div className='flex items-start gap-4 self-stretch'>
              <div className='flex flex-col items-start gap-1 self-stretch'>
                <div className='flex items-center gap-6 self-stretch'>
                  <div className='flex items-start gap-0.5 flex-[1_0_0]'>
                    <div className='flex flex-col justify-center items-start flex-[1_0_0] px-0 py-1 rounded-[2px_2px_0_0]'>
                      <span className='text-white font-source text-xs font-normal leading-[150%]'>
                        {t('settings.planarityError')}
                      </span>
                    </div>
                    <div className='flex flex-col justify-center items-center flex-[1_0_0] [background:var(--default-Gray-1,#333)] px-0 py-1 rounded-sm'>
                      <span className='text-white font-source text-xs font-normal leading-[150%]'>
                        {_.get(accuracyResult, 'planarity_error_mean', 0)} +- {_.get(accuracyResult, 'planarity_error_std', 0)} um
                      </span>
                    </div>
                  </div>
                </div>
                <div className='flex items-center gap-6 self-stretch'>
                  <div className='flex items-start gap-0.5 flex-[1_0_0]'>
                    <div className='flex flex-col justify-center items-start flex-[1_0_0] px-0 py-1 rounded-[2px_2px_0_0]'>
                      <span className='text-white font-source text-xs font-normal leading-[150%]'>
                        {t('settings.projectorDiscrepancy')}
                      </span>
                    </div>
                    <div className='flex flex-col justify-center items-center flex-[1_0_0] [background:var(--default-Gray-1,#333)] px-0 py-1 rounded-sm'>
                      <span className='text-white font-source text-xs font-normal leading-[150%]'>
                        {_.get(accuracyResult, 'projector_discrepancy_mean', 0)} +- {_.get(accuracyResult, 'projector_discrepancy_std', 0)} um
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div className='flex justify-center items-center gap-4 self-stretch px-0 py-4'>
              <Button
                onClick={() => {
                  setIsShowingAccuracy(false);
                  setAccuracyResult(null);
                  setIsOpened(false);
                }}
              >
                <span className='font-source text-[12px] font-normal leading-[150%]'>
                  {t('common.close')}
                </span>
              </Button>
            </div>
          </div>
        }
      </div>
    </CustomModal>
  );
};

export default ConveyorCalibrationProcess;