import React, { useState, useEffect, useRef } from "react";
import { useTranslation } from "react-i18next";
import _ from 'lodash';
import { serverHost, worklistMaxDisplayedCount } from "../../common/const";
import { useGetInspectedComponentAlterQuery } from "../../services/inference";
import { toLocalISOString } from "../../common/util";
import { Button } from "antd";


const NgComponent = (props) => {
  const {
    selectedGoldenProdId,
    startTime,
    endTime,
    snQuery,
    limit,
  } = props;

  const { t } = useTranslation();
  const tableRef = useRef(null);
  const [tableHeight, setTableHeight] = useState(0);

  useEffect(() => {
    if (!tableRef.current) return;

    const handleResize = () => {
      if (!tableRef.current) return;
      // Calculate available height: subtract header (56px) and table header (~42px) from parent height
      const availableHeight = tableRef.current.parentElement.offsetHeight - 56 - 42;
      setTableHeight(availableHeight);
    };

    const resizeObserver = new ResizeObserver(handleResize);
    resizeObserver.observe(tableRef.current.parentElement);
    handleResize();

    return () => {
      resizeObserver.disconnect();
    };
  }, []);

  const {
    data: ngComponents,
    isFetching: ngComponentsFetching,
    isLoading: ngComponentsLoading,
  } = useGetInspectedComponentAlterQuery({
    // is_feedback_correct: true,
    feedback_ok: false,
    prediction_ok: false,
    golden_product_id: _.isInteger(selectedGoldenProdId) ? Number(selectedGoldenProdId) : undefined,
    start_datetime: _.isNull(startTime) ? undefined : toLocalISOString(new Date(startTime)),
    end_datetime: _.isNull(endTime) ? undefined : toLocalISOString(new Date(endTime)),
    serial_no: _.isEmpty(snQuery) ? undefined : snQuery,
    limit,
  }, {
    refetchOnMountOrArgChange: true,
  });

  const filteredNgComponents = _.filter(ngComponents, rc => !rc.passing);

  const handleExportCSV = () => {
    if (_.isEmpty(filteredNgComponents)) {
      return;
    }

    // Define CSV headers
    const headers = [
      t('dataAnalysisReport.designator'),
      t('productDefine.partNo'),
      t('dataAnalysisReport.arrayBoardIndex'),
      t('dataAnalysisReport.status'),
      t('dataAnalysisReport.ngType'),
      t('dataAnalysisReport.predictedErrorType'),
    ];

    // Create CSV rows
    const rows = filteredNgComponents.map(record => [
      record.designator || '',
      record.part_no || '',
      record.subunit_index !== undefined ? record.subunit_index : '',
      record.passing ? t('common.pass') : t('common.ng'),
      // record.feedback_error_type || '',
      // record.predicted_error_type || '',
      _.isEmpty(record.feedback_error_type) ? '' : t(`inferenceErrorType.${record.feedback_error_type}`),
      _.isEmpty(record.predicted_error_type) ? '' : t(`inferenceErrorType.${record.predicted_error_type}`),
    ]);

    // Combine headers and rows
    const csvContent = [
      headers.join(','),
      ...rows.map(row => row.map(cell => `"${cell}"`).join(','))
    ].join('\n');

    // Create blob and download
    const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `ng_components_${new Date().getTime()}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  return (
    <>
      <style>{`
        @media print {
          body * {
            visibility: hidden;
          }
          .printable-section, .printable-section * {
            visibility: visible;
          }
          .printable-section {
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
            height: auto !important;
            overflow: visible !important;
          }
          .printable-section > div {
            height: auto !important;
            overflow: visible !important;
          }
          .printable-section > div > div:last-child {
            height: auto !important;
            overflow: visible !important;
          }
        }
      `}</style>
      <div className="flex flex-col items-center flex-[1_0_0] self-stretch px-4 py-0">
        <div className="flex items-center justify-between self-stretch px-0 py-2 h-[56px] gap-2">
          <span className="font-source text-base font-semibold leading-6">
            {t('dataAnalysisReport.ngComponent')} | {t('common.total')}: {_.get(ngComponents, 'length', 0)}
          </span>
          <Button
            onClick={handleExportCSV}
            disabled={_.isEmpty(filteredNgComponents)}
            className="flex items-center gap-1"
          >
            <img src="/icn/download_white.svg" alt="export" className="w-4 h-4" />
            <span className="font-source text-[12px] font-normal leading-[150%]">
              {t('common.export')} CSV
            </span>
          </Button>
        </div>
        <div className="printable-section flex flex-col items-start self-stretch flex-1 cursor-pointer" ref={tableRef}>
          <div className="flex flex-col items-start self-stretch rounded border border-[#333] border-solid" style={{ height: `${tableHeight}px` }}>
            <div className="flex items-center gap-4 self-stretch border-b-[#333] [background:rgba(0,0,0,0.03)] px-2 py-0 border-b border-solid sticky top-0 z-10">
              <div className="flex w-[120px] items-center gap-[20.81px]">
                <span className="flex-[1_0_0] font-source text-sm font-normal leading-[150%]">
                  {t('dataAnalysisReport.image')}
                </span>
              </div>
              <div className="flex h-[41.621px] items-center gap-2 flex-[1_0_0]">
                <div className="w-[1.301px] h-[41.621px] [background:#333]" />
                <span className="flex-[1_0_0] font-source text-sm font-normal leading-[150%]">
                  {t('dataAnalysisReport.designator')}
                </span>
                <div className="w-[1.301px] h-[41.621px] [background:#333]" />
                <span className="flex-[1_0_0] font-source text-sm font-normal leading-[150%]">
                  {t('productDefine.partNo')}
                </span>
                <div className="w-[1.301px] h-[41.621px] [background:#333]" />
                <span className="flex-[1_0_0] font-source text-sm font-normal leading-[150%]">
                  {t('dataAnalysisReport.arrayBoardIndex')}
                </span>
                <div className="w-[1.301px] h-[41.621px] [background:#333]" />
                <span className="flex-[1_0_0] font-source text-sm font-normal leading-[150%]">
                  {t('dataAnalysisReport.status')}
                </span>
                <div className="w-[1.301px] h-[41.621px] [background:#333]" />
                <span className="flex-[1_0_0] font-source text-sm font-normal leading-[150%]">
                  {t('dataAnalysisReport.ngType')}
                </span>
                <div className="w-[1.301px] h-[41.621px] [background:#333]" />
                <span className="flex-[1_0_0] font-source text-sm font-normal leading-[150%]">
                  {t('dataAnalysisReport.predictedErrorType')}
                </span>
              </div>
            </div>
            {ngComponentsLoading || ngComponentsFetching ? (
              <div className="flex justify-center items-center self-stretch flex-1">
                <span className="font-source text-sm font-normal leading-[150%]">{t('common.loading')}...</span>
              </div>
            ) : (
              <div className="flex flex-col items-start self-stretch overflow-auto flex-1">
                {_.map(filteredNgComponents, (rc, idx) => (
                  <InspectedComponentRow
                    key={idx}
                    inspectedComponent={rc}
                  />
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    </>
  );
};

const InspectedComponentRow = (props) => {
  const { inspectedComponent } = props;

  if (!inspectedComponent) return null;

  const { t } = useTranslation();

  const [url, setUrl] = useState('');

  useEffect(() => {
    let tmpUrl = `${serverHost}/blob?type=image`;
    tmpUrl += `&color_uri=${encodeURIComponent(_.get(inspectedComponent, 'color_map_uri', ''))}`;
    tmpUrl += `&depth_uri=${encodeURIComponent(_.get(inspectedComponent, 'depth_map_uri', ''))}`;
    tmpUrl += `&t=${Date.now().valueOf()}`;
    setUrl(tmpUrl);
  }, [inspectedComponent]);

  return (
    <div
      className="flex items-center gap-4 self-stretch border-b-[color:var(--default-Gray-6,#F2F2F2)] px-2 py-0 rounded-[5.203px] border-b-[1.301px] border-solid"
    >
      <div className="flex items-center gap-[13.007px] px-0 py-[5.203px]">
        <img
          src={url}
          alt="component"
          className="w-[120px] h-[120px] object-contain"
        />
      </div>
      <div className="flex items-center gap-2 flex-[1_0_0] self-stretch">
        <span
          className="flex-[1_0_0] font-source text-sm font-normal leading-[150%] overflow-hidden overflow-ellipsis whitespace-nowrap"
          title={inspectedComponent.designator}
        >
          {inspectedComponent.designator}
        </span>
        <div className="w-[1.301px] h-[130.405px] [background:#333]" />
        <span
          className="flex-[1_0_0] font-source text-sm font-normal leading-[150%] overflow-hidden overflow-ellipsis whitespace-nowrap"
          title={inspectedComponent.part_no}
        >
          {_.isEmpty(inspectedComponent.part_no) ? 'N/A' : inspectedComponent.part_no}
        </span>
        <div className="w-[1.301px] h-[130.405px] [background:#333]" />
        <span className="flex-[1_0_0] font-source text-sm font-normal leading-[150%] overflow-hidden overflow-ellipsis whitespace-nowrap">
          {inspectedComponent.array_index}
        </span>
        <div className="w-[1.301px] h-[130.405px] [background:#333]" />
        <span className="flex-[1_0_0] font-source text-sm font-normal leading-[150%] overflow-hidden overflow-ellipsis whitespace-nowrap">
          {inspectedComponent.passing ? t('dataAnalysisReport.good') : t('dataAnalysisReport.failed')}
        </span>
        <div className="w-[1.301px] h-[130.405px] [background:#333]" />
        <span className="flex-[1_0_0] font-source text-sm font-normal leading-[150%] overflow-hidden overflow-ellipsis whitespace-nowrap">
          {_.isEmpty(inspectedComponent.feedback_error_type) ? 'N/A' : t(`inferenceErrorType.${inspectedComponent.feedback_error_type}`)}
        </span>
        <div className="w-[1.301px] h-[130.405px] [background:#333]" />
        <span className="flex-[1_0_0] font-source text-sm font-normal leading-[150%] overflow-hidden overflow-ellipsis whitespace-nowrap">
          {_.isEmpty(inspectedComponent.predicted_error_type) ? 'N/A' : t(`inferenceErrorType.${inspectedComponent.predicted_error_type}`)}
        </span>
      </div>
    </div>
  );
};

export default NgComponent;