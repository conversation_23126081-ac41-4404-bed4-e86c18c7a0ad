import { ConfigProvider } from "antd";
import { useTranslation } from "react-i18next";
import { ContentRemovedTabs } from "../../common/styledComponent";
import Filter from "./Filter";
import { useEffect, useState } from "react";
import Summary from "./Summary";
import { useGetAllProductsQuery } from "../../services/product";
import InspectionResult from "./InspectionResult";
import moment from 'moment';
import dayjs from "dayjs";
import _ from 'lodash';
import NgComponent from "./NgComponent";
import FalsePositiveComponent from "./FalsePositiveComponent";
import { useDispatch, useSelector } from "react-redux";
import { setWorklistState } from "../../reducer/worklist";


const MES = () => {
  const { t } = useTranslation();
  const dispatch = useDispatch();

  // Get saved worklist state from Redux
  const savedWorklistState = useSelector((state) => state.worklist);

  const [activeTab, setActiveTab] = useState(savedWorklistState.activeTab || 'summary');
  const [selectedGoldenProdId, setSelectedGoldenProdId] = useState(savedWorklistState.selectedGoldenProdId);
  const [startTime, setStartTime] = useState(savedWorklistState.startTime ? dayjs(savedWorklistState.startTime) : null);
  const [endTime, setEndTime] = useState(savedWorklistState.endTime ? dayjs(savedWorklistState.endTime) : null);
  const [snQuery, setSnQuery] = useState(savedWorklistState.snQuery || '');
  const [queryInitialized, setQueryInitialized] = useState(false);
  const [quickTimeQueryType, setQuickTimeQueryType] = useState(savedWorklistState.quickTimeQueryType || 'today');
  const [limit, setLimit] = useState(savedWorklistState.limit || 1000);

  const {
    data: allProducts,
    isError: allProductsError,
    isLoading: allProductsLoading,
    refetch: refetchAllProducts,
    isFetching: allProductsFetching
  } = useGetAllProductsQuery();

  useEffect(() => {
    if (_.isEmpty(quickTimeQueryType)) return;

    if (quickTimeQueryType === 'today') {
      setStartTime(dayjs(moment().startOf('day').toDate()));
      setEndTime(dayjs(moment().endOf('day').toDate()));
    } else if (quickTimeQueryType === 'yesterday') {
      setStartTime(dayjs(moment().subtract(1, 'day').startOf('day').toDate()));
      setEndTime(dayjs(moment().subtract(1, 'day').endOf('day').toDate()));
    } else if (quickTimeQueryType === 'thisWeek') {
      setStartTime(dayjs(moment().startOf('week').toDate()));
      setEndTime(dayjs(moment().endOf('week').toDate()));
    } else if (quickTimeQueryType === 'thisMonth') {
      setStartTime(dayjs(moment().startOf('month').toDate()));
      setEndTime(dayjs(moment().endOf('month').toDate()));
    } else if (quickTimeQueryType === 'thisYear') {
      setStartTime(dayjs(moment().startOf('year').toDate()));
      setEndTime(dayjs(moment().endOf('year').toDate()));
    }
  }, [quickTimeQueryType]);

  useEffect(() => {
    // Initialize start time and end time only if not already set from saved state
    if (!savedWorklistState.startTime || !savedWorklistState.endTime) {
      setStartTime(dayjs(moment().startOf('day').toDate()));
      setEndTime(dayjs(moment().endOf('day').toDate()));
    }
    setQueryInitialized(true);
  }, []);

  // Save worklist state to Redux whenever it changes
  useEffect(() => {
    if (!queryInitialized) return;

    dispatch(setWorklistState({
      activeTab,
      selectedGoldenProdId,
      startTime: startTime ? startTime.toISOString() : null,
      endTime: endTime ? endTime.toISOString() : null,
      snQuery,
      quickTimeQueryType,
      limit,
    }));
  }, [activeTab, selectedGoldenProdId, startTime, endTime, snQuery, quickTimeQueryType, limit, queryInitialized, dispatch]);

  if (
    allProductsFetching ||
    allProductsLoading ||
    !queryInitialized
  ) return null;

  return (
    <div className='flex flex-col items-start flex-1 self-stretch pt-3 pb-6 px-8'>
      <div className='flex h-12 justify-between items-center self-stretch border-b-[color:var(--default-Gray-1,#333)] pt-1 pb-0 px-3 border-b border-solid'>
        <div className='flex items-baseline gap-6 self-stretch'>
          <div className='flex flex-col items-center justify-end self-stretch'>
            <span className='font-source text-lg font-normal leading-[normal]'>
              {t('dataAnalysisReport.dataAnalysisReport')}
            </span>
          </div>
          <div className='flex flex-1 flex-col items-center justify-end self-stretch'>
            <ConfigProvider
              theme={{
                components: {
                  Tabs: {
                    cardPadding: '4px 12px',
                    horizontalMargin: '4px 4px 0 4px',
                    colorBgContainer: '#56CCF2',
                    colorBorder: '#4F4F4F',
                    cardBg: '#1E1E1E',
                    itemSelectedColor: '#000',
                    itemHoverColor: '#fff',
                    itemActiveColor: '#000',
                  },
                }
              }}
            >
              <ContentRemovedTabs
                type='card'
                activeKey={activeTab}
                onChange={(key) => setActiveTab(key)}
                items={[
                  {
                    key: 'summary',
                    label: <span className={`font-source text-sm font-${activeTab === 'summary' ? 'semibold' : 'normal'} leading-[normal]`}>
                      {t('dataAnalysisReport.summary')}
                    </span>,
                    children: null,
                  },
                  {
                    key: 'inspectionResult',
                    label: <span className={`font-source text-sm font-${activeTab === 'inspectionResult' ? 'semibold' : 'normal'} leading-[normal]`}>
                      {t('dataAnalysisReport.inspectionResult')}
                    </span>,
                    children: null,
                  },
                  {
                    key: 'ngComponent',
                    label: <span className={`font-source text-sm font-${activeTab === 'ngComponent' ? 'semibold' : 'normal'} leading-[normal]`}>
                      {t('dataAnalysisReport.ngComponent')}
                    </span>,
                    children: null,
                  },
                  {
                    key: 'falsePositiveComponent',
                    label: <span className={`font-source text-sm font-${activeTab === 'falsePositiveComponent' ? 'semibold' : 'normal'} leading-[normal]`}>
                      {t('dataAnalysisReport.falsePositiveComponent')}
                    </span>,
                    children: null,
                  }
                ]}
              />
            </ConfigProvider>
          </div>
        </div>
      </div>
      <div className='flex items-start gap-8 flex-1 self-stretch [background:rgba(255,255,255,0.03)]'>
        <div className='flex flex-col items-center gap-2 flex-1 self-stretch'>
          <Filter
            selectedGoldenProdId={selectedGoldenProdId}
            setSelectedGoldenProdId={setSelectedGoldenProdId}
            allProducts={allProducts}
            startTime={startTime}
            setStartTime={setStartTime}
            endTime={endTime}
            setEndTime={setEndTime}
            snQuery={snQuery}
            setSnQuery={setSnQuery}
            quickTimeQueryType={quickTimeQueryType}
            setQuickTimeQueryType={setQuickTimeQueryType}
            limit={limit}
            setLimit={setLimit}
            activeTab={activeTab}
          />
          {activeTab === 'summary' &&
            <Summary
              selectedGoldenProdId={selectedGoldenProdId}
              startTime={startTime}
              endTime={endTime}
              snQuery={snQuery}
            />
          }
          {activeTab === 'inspectionResult' &&
            <InspectionResult
              selectedGoldenProdId={selectedGoldenProdId}
              startTime={startTime}
              endTime={endTime}
              snQuery={snQuery}
              limit={limit}
            />
          }
          {activeTab === 'ngComponent' &&
            <NgComponent
              selectedGoldenProdId={selectedGoldenProdId}
              startTime={startTime}
              endTime={endTime}
              snQuery={snQuery}
              limit={limit}
            />
          }
          {activeTab === 'falsePositiveComponent' &&
            <FalsePositiveComponent
              selectedGoldenProdId={selectedGoldenProdId}
              startTime={startTime}
              endTime={endTime}
              snQuery={snQuery}
              limit={limit}
            />
          }
        </div>
      </div>
    </div>
  );
};

export default MES;